<?php
/**
 * User Management Functions
 *
 * @package IPTV_Pro
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Add custom user fields
 */
function iptv_pro_user_profile_fields($user) {
    ?>
    <h3><?php _e('IPTV Customer Information', 'iptv-pro'); ?></h3>
    <table class="form-table">
        <tr>
            <th><label for="customer_id"><?php _e('Customer ID', 'iptv-pro'); ?></label></th>
            <td>
                <input type="text" name="customer_id" id="customer_id" value="<?php echo esc_attr(get_user_meta($user->ID, 'customer_id', true)); ?>" class="regular-text" readonly />
                <p class="description"><?php _e('Unique customer identifier', 'iptv-pro'); ?></p>
            </td>
        </tr>
        <tr>
            <th><label for="subscription_status"><?php _e('Subscription Status', 'iptv-pro'); ?></label></th>
            <td>
                <select name="subscription_status" id="subscription_status">
                    <option value="active" <?php selected(get_user_meta($user->ID, 'subscription_status', true), 'active'); ?>><?php _e('Active', 'iptv-pro'); ?></option>
                    <option value="expired" <?php selected(get_user_meta($user->ID, 'subscription_status', true), 'expired'); ?>><?php _e('Expired', 'iptv-pro'); ?></option>
                    <option value="cancelled" <?php selected(get_user_meta($user->ID, 'subscription_status', true), 'cancelled'); ?>><?php _e('Cancelled', 'iptv-pro'); ?></option>
                    <option value="suspended" <?php selected(get_user_meta($user->ID, 'subscription_status', true), 'suspended'); ?>><?php _e('Suspended', 'iptv-pro'); ?></option>
                </select>
            </td>
        </tr>
        <tr>
            <th><label for="subscription_end_date"><?php _e('Subscription End Date', 'iptv-pro'); ?></label></th>
            <td>
                <input type="date" name="subscription_end_date" id="subscription_end_date" value="<?php echo esc_attr(get_user_meta($user->ID, 'subscription_end_date', true)); ?>" class="regular-text" />
            </td>
        </tr>
        <tr>
            <th><label for="iptv_username"><?php _e('IPTV Username', 'iptv-pro'); ?></label></th>
            <td>
                <input type="text" name="iptv_username" id="iptv_username" value="<?php echo esc_attr(get_user_meta($user->ID, 'iptv_username', true)); ?>" class="regular-text" />
            </td>
        </tr>
        <tr>
            <th><label for="iptv_password"><?php _e('IPTV Password', 'iptv-pro'); ?></label></th>
            <td>
                <input type="text" name="iptv_password" id="iptv_password" value="<?php echo esc_attr(get_user_meta($user->ID, 'iptv_password', true)); ?>" class="regular-text" />
            </td>
        </tr>
        <tr>
            <th><label for="server_url"><?php _e('Server URL', 'iptv-pro'); ?></label></th>
            <td>
                <input type="url" name="server_url" id="server_url" value="<?php echo esc_attr(get_user_meta($user->ID, 'server_url', true)); ?>" class="regular-text" />
            </td>
        </tr>
        <tr>
            <th><label for="max_connections"><?php _e('Max Connections', 'iptv-pro'); ?></label></th>
            <td>
                <input type="number" name="max_connections" id="max_connections" value="<?php echo esc_attr(get_user_meta($user->ID, 'max_connections', true)); ?>" class="small-text" min="1" max="10" />
            </td>
        </tr>
        <tr>
            <th><label for="package_type"><?php _e('Package Type', 'iptv-pro'); ?></label></th>
            <td>
                <select name="package_type" id="package_type">
                    <option value=""><?php _e('Select Package', 'iptv-pro'); ?></option>
                    <?php
                    $packages = get_posts(array(
                        'post_type' => 'iptv_package',
                        'posts_per_page' => -1,
                        'post_status' => 'publish'
                    ));
                    $current_package = get_user_meta($user->ID, 'package_type', true);
                    foreach ($packages as $package) {
                        echo '<option value="' . $package->ID . '" ' . selected($current_package, $package->ID, false) . '>' . $package->post_title . '</option>';
                    }
                    ?>
                </select>
            </td>
        </tr>
    </table>
    <?php
}
add_action('show_user_profile', 'iptv_pro_user_profile_fields');
add_action('edit_user_profile', 'iptv_pro_user_profile_fields');

/**
 * Save custom user fields
 */
function iptv_pro_save_user_profile_fields($user_id) {
    if (!current_user_can('edit_user', $user_id)) {
        return false;
    }

    $fields = array(
        'customer_id',
        'subscription_status',
        'subscription_end_date',
        'iptv_username',
        'iptv_password',
        'server_url',
        'max_connections',
        'package_type'
    );

    foreach ($fields as $field) {
        if (isset($_POST[$field])) {
            update_user_meta($user_id, $field, sanitize_text_field($_POST[$field]));
        }
    }
}
add_action('personal_options_update', 'iptv_pro_save_user_profile_fields');
add_action('edit_user_profile_update', 'iptv_pro_save_user_profile_fields');

/**
 * Generate unique customer ID on user registration
 */
function iptv_pro_generate_customer_id($user_id) {
    $customer_id = 'IPTV' . str_pad($user_id, 6, '0', STR_PAD_LEFT);
    update_user_meta($user_id, 'customer_id', $customer_id);
    
    // Set default values
    update_user_meta($user_id, 'subscription_status', 'inactive');
    update_user_meta($user_id, 'max_connections', 1);
}
add_action('user_register', 'iptv_pro_generate_customer_id');

/**
 * Custom user registration form
 */
function iptv_pro_registration_form() {
    ?>
    <p>
        <label for="first_name"><?php _e('First Name', 'iptv-pro'); ?> <span class="required">*</span></label>
        <input type="text" name="first_name" id="first_name" class="input" value="<?php echo esc_attr(wp_unslash($_POST['first_name'] ?? '')); ?>" size="25" required />
    </p>
    <p>
        <label for="last_name"><?php _e('Last Name', 'iptv-pro'); ?> <span class="required">*</span></label>
        <input type="text" name="last_name" id="last_name" class="input" value="<?php echo esc_attr(wp_unslash($_POST['last_name'] ?? '')); ?>" size="25" required />
    </p>
    <p>
        <label for="phone"><?php _e('Phone Number', 'iptv-pro'); ?></label>
        <input type="tel" name="phone" id="phone" class="input" value="<?php echo esc_attr(wp_unslash($_POST['phone'] ?? '')); ?>" size="25" />
    </p>
    <p>
        <label for="country"><?php _e('Country', 'iptv-pro'); ?></label>
        <select name="country" id="country" class="input">
            <option value=""><?php _e('Select Country', 'iptv-pro'); ?></option>
            <option value="US"><?php _e('United States', 'iptv-pro'); ?></option>
            <option value="CA"><?php _e('Canada', 'iptv-pro'); ?></option>
            <option value="UK"><?php _e('United Kingdom', 'iptv-pro'); ?></option>
            <option value="DE"><?php _e('Germany', 'iptv-pro'); ?></option>
            <option value="FR"><?php _e('France', 'iptv-pro'); ?></option>
            <option value="ES"><?php _e('Spain', 'iptv-pro'); ?></option>
            <option value="IT"><?php _e('Italy', 'iptv-pro'); ?></option>
            <option value="AU"><?php _e('Australia', 'iptv-pro'); ?></option>
            <!-- Add more countries as needed -->
        </select>
    </p>
    <?php
}
add_action('register_form', 'iptv_pro_registration_form');

/**
 * Validate registration form
 */
function iptv_pro_registration_errors($errors, $sanitized_user_login, $user_email) {
    if (empty($_POST['first_name'])) {
        $errors->add('first_name_error', __('First name is required.', 'iptv-pro'));
    }
    
    if (empty($_POST['last_name'])) {
        $errors->add('last_name_error', __('Last name is required.', 'iptv-pro'));
    }
    
    return $errors;
}
add_filter('registration_errors', 'iptv_pro_registration_errors', 10, 3);

/**
 * Save additional registration fields
 */
function iptv_pro_save_registration_fields($user_id) {
    if (isset($_POST['first_name'])) {
        update_user_meta($user_id, 'first_name', sanitize_text_field($_POST['first_name']));
    }
    
    if (isset($_POST['last_name'])) {
        update_user_meta($user_id, 'last_name', sanitize_text_field($_POST['last_name']));
    }
    
    if (isset($_POST['phone'])) {
        update_user_meta($user_id, 'phone', sanitize_text_field($_POST['phone']));
    }
    
    if (isset($_POST['country'])) {
        update_user_meta($user_id, 'country', sanitize_text_field($_POST['country']));
    }
}
add_action('user_register', 'iptv_pro_save_registration_fields');

/**
 * Check subscription status
 */
function iptv_pro_check_subscription_status($user_id = null) {
    if (!$user_id) {
        $user_id = get_current_user_id();
    }
    
    if (!$user_id) {
        return false;
    }
    
    $status = get_user_meta($user_id, 'subscription_status', true);
    $end_date = get_user_meta($user_id, 'subscription_end_date', true);
    
    // Check if subscription has expired
    if ($end_date && strtotime($end_date) < time() && $status === 'active') {
        update_user_meta($user_id, 'subscription_status', 'expired');
        $status = 'expired';
    }
    
    return $status === 'active';
}

/**
 * Get user subscription info
 */
function iptv_pro_get_user_subscription_info($user_id = null) {
    if (!$user_id) {
        $user_id = get_current_user_id();
    }
    
    if (!$user_id) {
        return false;
    }
    
    return array(
        'customer_id' => get_user_meta($user_id, 'customer_id', true),
        'status' => get_user_meta($user_id, 'subscription_status', true),
        'end_date' => get_user_meta($user_id, 'subscription_end_date', true),
        'iptv_username' => get_user_meta($user_id, 'iptv_username', true),
        'iptv_password' => get_user_meta($user_id, 'iptv_password', true),
        'server_url' => get_user_meta($user_id, 'server_url', true),
        'max_connections' => get_user_meta($user_id, 'max_connections', true),
        'package_type' => get_user_meta($user_id, 'package_type', true),
    );
}

/**
 * Generate IPTV credentials
 */
function iptv_pro_generate_iptv_credentials($user_id) {
    $username = 'iptv_' . get_user_meta($user_id, 'customer_id', true);
    $password = wp_generate_password(12, false);
    
    update_user_meta($user_id, 'iptv_username', $username);
    update_user_meta($user_id, 'iptv_password', $password);
    
    return array(
        'username' => $username,
        'password' => $password
    );
}

/**
 * Send welcome email with IPTV credentials
 */
function iptv_pro_send_welcome_email($user_id) {
    $user = get_userdata($user_id);
    $credentials = iptv_pro_generate_iptv_credentials($user_id);
    
    $subject = sprintf(__('Welcome to %s - Your IPTV Credentials', 'iptv-pro'), get_bloginfo('name'));
    
    $message = sprintf(
        __('Hello %s,

Welcome to %s! Your IPTV account has been created successfully.

Your IPTV Credentials:
Username: %s
Password: %s
Server URL: %s

You can manage your subscription and download apps from your dashboard:
%s

If you need any assistance, please contact our support team.

Best regards,
%s Team', 'iptv-pro'),
        $user->display_name,
        get_bloginfo('name'),
        $credentials['username'],
        $credentials['password'],
        get_theme_mod('default_server_url', 'http://your-server.com:8080'),
        get_permalink(get_page_by_path('dashboard')),
        get_bloginfo('name')
    );
    
    wp_mail($user->user_email, $subject, $message);
}

/**
 * Add custom user columns to admin
 */
function iptv_pro_user_columns($columns) {
    $columns['customer_id'] = __('Customer ID', 'iptv-pro');
    $columns['subscription_status'] = __('Status', 'iptv-pro');
    $columns['subscription_end'] = __('Expires', 'iptv-pro');
    return $columns;
}
add_filter('manage_users_columns', 'iptv_pro_user_columns');

/**
 * Display custom user column content
 */
function iptv_pro_user_column_content($value, $column_name, $user_id) {
    switch ($column_name) {
        case 'customer_id':
            return get_user_meta($user_id, 'customer_id', true);
        case 'subscription_status':
            $status = get_user_meta($user_id, 'subscription_status', true);
            $colors = array(
                'active' => '#10b981',
                'expired' => '#ef4444',
                'cancelled' => '#6b7280',
                'suspended' => '#f59e0b'
            );
            $color = $colors[$status] ?? '#6b7280';
            return '<span style="color: ' . $color . '; font-weight: bold;">' . ucfirst($status) . '</span>';
        case 'subscription_end':
            $end_date = get_user_meta($user_id, 'subscription_end_date', true);
            return $end_date ? date('M j, Y', strtotime($end_date)) : '-';
    }
    return $value;
}
add_action('manage_users_custom_column', 'iptv_pro_user_column_content', 10, 3);

?>
