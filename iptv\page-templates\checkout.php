<?php
/**
 * Template Name: Checkout
 *
 * @package IPTV_Pro
 */

// Redirect if not logged in
if (!is_user_logged_in()) {
    wp_redirect(add_query_arg('redirect_to', urlencode(get_permalink()), wp_login_url()));
    exit;
}

$package_id = isset($_GET['package']) ? intval($_GET['package']) : 0;
$package = $package_id ? get_post($package_id) : null;

if (!$package || $package->post_type !== 'iptv_package') {
    wp_redirect(get_permalink(get_page_by_path('packages')));
    exit;
}

$price = get_post_meta($package_id, '_iptv_price', true);
$duration = get_post_meta($package_id, '_iptv_duration', true);
$channels = get_post_meta($package_id, '_iptv_channels', true);
$features = get_post_meta($package_id, '_iptv_features', true);

get_header();
?>

<main class="main-content checkout-page">
    <div class="container" style="padding: 2rem 0;">
        
        <!-- Checkout Header -->
        <div class="checkout-header" style="text-align: center; margin-bottom: 3rem;">
            <h1 style="font-size: 2.5rem; font-weight: 700; margin-bottom: 1rem; color: #1f2937;">
                <?php _e('Complete Your Order', 'iptv-pro'); ?>
            </h1>
            <p style="color: #6b7280; font-size: 1.125rem;">
                <?php _e('You\'re just one step away from enjoying premium IPTV service!', 'iptv-pro'); ?>
            </p>
        </div>

        <div style="display: grid; grid-template-columns: 1fr 400px; gap: 3rem; max-width: 1000px; margin: 0 auto;">
            
            <!-- Payment Form -->
            <div class="payment-section">
                <div style="background: white; padding: 2rem; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.05);">
                    <h2 style="font-size: 1.5rem; font-weight: 600; margin-bottom: 2rem; color: #1f2937;">
                        <?php _e('Payment Information', 'iptv-pro'); ?>
                    </h2>
                    
                    <form class="checkout-form" id="checkout-form">
                        <input type="hidden" name="package_id" value="<?php echo $package_id; ?>">
                        
                        <!-- Payment Methods -->
                        <div class="payment-methods" style="margin-bottom: 2rem;">
                            <h3 style="font-size: 1.125rem; font-weight: 600; margin-bottom: 1rem; color: #374151;">
                                <?php _e('Select Payment Method', 'iptv-pro'); ?>
                            </h3>
                            
                            <div style="display: flex; flex-direction: column; gap: 1rem;">
                                <?php if (get_theme_mod('enable_paypal', true)) : ?>
                                    <label style="display: flex; align-items: center; padding: 1rem; border: 2px solid #e5e7eb; border-radius: 8px; cursor: pointer; transition: border-color 0.3s;">
                                        <input type="radio" name="payment_method" value="paypal" class="payment-method" style="margin-right: 1rem;">
                                        <div style="display: flex; align-items: center; gap: 1rem;">
                                            <div style="font-size: 2rem;">💳</div>
                                            <div>
                                                <div style="font-weight: 600; color: #1f2937;"><?php _e('PayPal', 'iptv-pro'); ?></div>
                                                <div style="font-size: 0.875rem; color: #6b7280;"><?php _e('Pay securely with PayPal', 'iptv-pro'); ?></div>
                                            </div>
                                        </div>
                                    </label>
                                <?php endif; ?>
                                
                                <?php if (get_theme_mod('enable_stripe', true)) : ?>
                                    <label style="display: flex; align-items: center; padding: 1rem; border: 2px solid #e5e7eb; border-radius: 8px; cursor: pointer; transition: border-color 0.3s;">
                                        <input type="radio" name="payment_method" value="stripe" class="payment-method" style="margin-right: 1rem;">
                                        <div style="display: flex; align-items: center; gap: 1rem;">
                                            <div style="font-size: 2rem;">💳</div>
                                            <div>
                                                <div style="font-weight: 600; color: #1f2937;"><?php _e('Credit/Debit Card', 'iptv-pro'); ?></div>
                                                <div style="font-size: 0.875rem; color: #6b7280;"><?php _e('Visa, Mastercard, American Express', 'iptv-pro'); ?></div>
                                            </div>
                                        </div>
                                    </label>
                                <?php endif; ?>
                                
                                <?php if (get_theme_mod('enable_crypto', false)) : ?>
                                    <label style="display: flex; align-items: center; padding: 1rem; border: 2px solid #e5e7eb; border-radius: 8px; cursor: pointer; transition: border-color 0.3s;">
                                        <input type="radio" name="payment_method" value="crypto" class="payment-method" style="margin-right: 1rem;">
                                        <div style="display: flex; align-items: center; gap: 1rem;">
                                            <div style="font-size: 2rem;">₿</div>
                                            <div>
                                                <div style="font-weight: 600; color: #1f2937;"><?php _e('Cryptocurrency', 'iptv-pro'); ?></div>
                                                <div style="font-size: 0.875rem; color: #6b7280;"><?php _e('Bitcoin, Ethereum, USDT', 'iptv-pro'); ?></div>
                                            </div>
                                        </div>
                                    </label>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- Payment Details -->
                        <div id="stripe-details" class="payment-details" style="display: none; margin-bottom: 2rem;">
                            <h3 style="font-size: 1.125rem; font-weight: 600; margin-bottom: 1rem; color: #374151;">
                                <?php _e('Card Information', 'iptv-pro'); ?>
                            </h3>
                            
                            <div style="display: grid; gap: 1rem;">
                                <div>
                                    <label style="display: block; font-weight: 500; margin-bottom: 0.5rem; color: #374151;">
                                        <?php _e('Card Number', 'iptv-pro'); ?>
                                    </label>
                                    <input type="text" name="card_number" placeholder="1234 5678 9012 3456" style="width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 6px;">
                                </div>
                                
                                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                                    <div>
                                        <label style="display: block; font-weight: 500; margin-bottom: 0.5rem; color: #374151;">
                                            <?php _e('Expiry Date', 'iptv-pro'); ?>
                                        </label>
                                        <input type="text" name="card_expiry" placeholder="MM/YY" style="width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 6px;">
                                    </div>
                                    
                                    <div>
                                        <label style="display: block; font-weight: 500; margin-bottom: 0.5rem; color: #374151;">
                                            <?php _e('CVC', 'iptv-pro'); ?>
                                        </label>
                                        <input type="text" name="card_cvc" placeholder="123" style="width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 6px;">
                                    </div>
                                </div>
                                
                                <div>
                                    <label style="display: block; font-weight: 500; margin-bottom: 0.5rem; color: #374151;">
                                        <?php _e('Cardholder Name', 'iptv-pro'); ?>
                                    </label>
                                    <input type="text" name="card_name" placeholder="John Doe" style="width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 6px;">
                                </div>
                            </div>
                        </div>

                        <div id="crypto-details" class="payment-details" style="display: none; margin-bottom: 2rem;">
                            <h3 style="font-size: 1.125rem; font-weight: 600; margin-bottom: 1rem; color: #374151;">
                                <?php _e('Cryptocurrency Payment', 'iptv-pro'); ?>
                            </h3>
                            <p style="color: #6b7280; margin-bottom: 1rem;">
                                <?php _e('After clicking "Complete Payment", you will receive the cryptocurrency address and amount to send.', 'iptv-pro'); ?>
                            </p>
                        </div>

                        <!-- Billing Information -->
                        <div class="billing-info" style="margin-bottom: 2rem;">
                            <h3 style="font-size: 1.125rem; font-weight: 600; margin-bottom: 1rem; color: #374151;">
                                <?php _e('Billing Information', 'iptv-pro'); ?>
                            </h3>
                            
                            <?php $current_user = wp_get_current_user(); ?>
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                                <div>
                                    <label style="display: block; font-weight: 500; margin-bottom: 0.5rem; color: #374151;">
                                        <?php _e('First Name', 'iptv-pro'); ?>
                                    </label>
                                    <input type="text" name="first_name" value="<?php echo esc_attr($current_user->first_name); ?>" required style="width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 6px;">
                                </div>
                                
                                <div>
                                    <label style="display: block; font-weight: 500; margin-bottom: 0.5rem; color: #374151;">
                                        <?php _e('Last Name', 'iptv-pro'); ?>
                                    </label>
                                    <input type="text" name="last_name" value="<?php echo esc_attr($current_user->last_name); ?>" required style="width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 6px;">
                                </div>
                            </div>
                            
                            <div style="margin-top: 1rem;">
                                <label style="display: block; font-weight: 500; margin-bottom: 0.5rem; color: #374151;">
                                    <?php _e('Email Address', 'iptv-pro'); ?>
                                </label>
                                <input type="email" name="email" value="<?php echo esc_attr($current_user->user_email); ?>" required style="width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 6px;">
                            </div>
                        </div>

                        <!-- Terms and Conditions -->
                        <div style="margin-bottom: 2rem;">
                            <label style="display: flex; align-items: start; gap: 0.75rem; cursor: pointer;">
                                <input type="checkbox" name="terms_accepted" required style="margin-top: 0.25rem;">
                                <span style="color: #374151; font-size: 0.875rem;">
                                    <?php printf(
                                        __('I agree to the %s and %s', 'iptv-pro'),
                                        '<a href="' . get_permalink(get_page_by_path('terms-of-service')) . '" target="_blank" style="color: #3b82f6;">' . __('Terms of Service', 'iptv-pro') . '</a>',
                                        '<a href="' . get_permalink(get_page_by_path('privacy-policy')) . '" target="_blank" style="color: #3b82f6;">' . __('Privacy Policy', 'iptv-pro') . '</a>'
                                    ); ?>
                                </span>
                            </label>
                        </div>

                        <!-- Submit Button -->
                        <button type="submit" class="btn btn-primary submit-btn" style="width: 100%; font-size: 1.125rem; padding: 1rem;">
                            <?php printf(__('Complete Payment - $%s', 'iptv-pro'), number_format($price, 2)); ?>
                        </button>
                    </form>
                </div>
            </div>

            <!-- Order Summary -->
            <div class="order-summary">
                <div style="background: white; padding: 2rem; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.05); position: sticky; top: 2rem;">
                    <h2 style="font-size: 1.5rem; font-weight: 600; margin-bottom: 2rem; color: #1f2937;">
                        <?php _e('Order Summary', 'iptv-pro'); ?>
                    </h2>
                    
                    <div class="package-summary" style="margin-bottom: 2rem;">
                        <h3 style="font-size: 1.25rem; font-weight: 600; margin-bottom: 1rem; color: #1f2937;">
                            <?php echo esc_html($package->post_title); ?>
                        </h3>
                        
                        <div style="background: #f8fafc; padding: 1.5rem; border-radius: 8px; margin-bottom: 1.5rem;">
                            <div style="display: flex; justify-content: space-between; margin-bottom: 0.75rem;">
                                <span style="color: #6b7280;"><?php _e('Duration:', 'iptv-pro'); ?></span>
                                <span style="font-weight: 600; color: #1f2937;"><?php echo $duration; ?> <?php _e('months', 'iptv-pro'); ?></span>
                            </div>
                            
                            <div style="display: flex; justify-content: space-between; margin-bottom: 0.75rem;">
                                <span style="color: #6b7280;"><?php _e('Channels:', 'iptv-pro'); ?></span>
                                <span style="font-weight: 600; color: #1f2937;"><?php echo $channels; ?>+</span>
                            </div>
                            
                            <div style="display: flex; justify-content: space-between; margin-bottom: 0.75rem;">
                                <span style="color: #6b7280;"><?php _e('Quality:', 'iptv-pro'); ?></span>
                                <span style="font-weight: 600; color: #1f2937;"><?php _e('HD & 4K', 'iptv-pro'); ?></span>
                            </div>
                            
                            <div style="display: flex; justify-content: space-between;">
                                <span style="color: #6b7280;"><?php _e('Support:', 'iptv-pro'); ?></span>
                                <span style="font-weight: 600; color: #1f2937;"><?php _e('24/7', 'iptv-pro'); ?></span>
                            </div>
                        </div>
                        
                        <?php if ($features) : ?>
                            <div style="margin-bottom: 1.5rem;">
                                <h4 style="font-weight: 600; margin-bottom: 0.75rem; color: #374151;"><?php _e('Included Features:', 'iptv-pro'); ?></h4>
                                <ul style="list-style: none; padding: 0;">
                                    <?php
                                    $feature_list = array_slice(explode("\n", $features), 0, 5);
                                    foreach ($feature_list as $feature) {
                                        if (trim($feature)) {
                                            echo '<li style="padding: 0.25rem 0; display: flex; align-items: center; color: #4b5563; font-size: 0.875rem;">';
                                            echo '<span style="color: #10b981; font-weight: bold; margin-right: 0.5rem;">✓</span>';
                                            echo esc_html(trim($feature));
                                            echo '</li>';
                                        }
                                    }
                                    ?>
                                </ul>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <!-- Price Breakdown -->
                    <div style="border-top: 1px solid #e5e7eb; padding-top: 1.5rem;">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 0.75rem;">
                            <span style="color: #6b7280;"><?php _e('Subtotal:', 'iptv-pro'); ?></span>
                            <span style="color: #1f2937;">$<?php echo number_format($price, 2); ?></span>
                        </div>
                        
                        <div style="display: flex; justify-content: space-between; margin-bottom: 0.75rem;">
                            <span style="color: #6b7280;"><?php _e('Tax:', 'iptv-pro'); ?></span>
                            <span style="color: #1f2937;">$0.00</span>
                        </div>
                        
                        <div style="border-top: 1px solid #e5e7eb; padding-top: 0.75rem; display: flex; justify-content: space-between;">
                            <span style="font-weight: 700; font-size: 1.125rem; color: #1f2937;"><?php _e('Total:', 'iptv-pro'); ?></span>
                            <span style="font-weight: 700; font-size: 1.125rem; color: #3b82f6;">$<?php echo number_format($price, 2); ?></span>
                        </div>
                    </div>
                    
                    <!-- Security Notice -->
                    <div style="margin-top: 2rem; padding: 1rem; background: #f0f9ff; border: 1px solid #bae6fd; border-radius: 8px;">
                        <div style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 0.5rem;">
                            <span style="color: #0369a1; font-size: 1.25rem;">🔒</span>
                            <span style="font-weight: 600; color: #0369a1; font-size: 0.875rem;"><?php _e('Secure Payment', 'iptv-pro'); ?></span>
                        </div>
                        <p style="color: #0369a1; font-size: 0.75rem; margin: 0;">
                            <?php _e('Your payment information is encrypted and secure. We never store your card details.', 'iptv-pro'); ?>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const paymentMethods = document.querySelectorAll('.payment-method');
    const paymentDetails = document.querySelectorAll('.payment-details');
    
    // Payment method selection
    paymentMethods.forEach(method => {
        method.addEventListener('change', function() {
            // Update label styles
            paymentMethods.forEach(m => {
                m.closest('label').style.borderColor = '#e5e7eb';
                m.closest('label').style.backgroundColor = 'white';
            });
            
            if (this.checked) {
                this.closest('label').style.borderColor = '#3b82f6';
                this.closest('label').style.backgroundColor = '#f0f9ff';
            }
            
            // Show/hide payment details
            paymentDetails.forEach(detail => {
                detail.style.display = 'none';
            });
            
            const targetDetail = document.getElementById(this.value + '-details');
            if (targetDetail) {
                targetDetail.style.display = 'block';
            }
        });
    });
    
    // Form submission
    const checkoutForm = document.getElementById('checkout-form');
    checkoutForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        formData.append('action', 'iptv_process_payment');
        formData.append('nonce', iptv_ajax.nonce);
        
        const submitBtn = this.querySelector('.submit-btn');
        const originalText = submitBtn.textContent;
        submitBtn.disabled = true;
        submitBtn.textContent = '<?php _e('Processing...', 'iptv-pro'); ?>';
        
        fetch(iptv_ajax.ajax_url, {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                if (data.data.redirect_url) {
                    window.location.href = data.data.redirect_url;
                } else if (data.data.crypto_address) {
                    // Handle crypto payment
                    showCryptoPayment(data.data);
                } else {
                    alert('<?php _e('Payment processed successfully!', 'iptv-pro'); ?>');
                    window.location.href = '<?php echo get_permalink(get_page_by_path('dashboard')); ?>';
                }
            } else {
                alert(data.data || '<?php _e('Payment failed. Please try again.', 'iptv-pro'); ?>');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('<?php _e('Network error. Please try again.', 'iptv-pro'); ?>');
        })
        .finally(() => {
            submitBtn.disabled = false;
            submitBtn.textContent = originalText;
        });
    });
});

function showCryptoPayment(data) {
    const modal = document.createElement('div');
    modal.style.cssText = 'position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0,0,0,0.5); display: flex; align-items: center; justify-content: center; z-index: 10000;';
    
    modal.innerHTML = `
        <div style="background: white; padding: 2rem; border-radius: 12px; max-width: 500px; width: 90%;">
            <h3 style="margin-bottom: 1rem;"><?php _e('Cryptocurrency Payment', 'iptv-pro'); ?></h3>
            <p style="margin-bottom: 1rem;"><?php _e('Please send the exact amount to the address below:', 'iptv-pro'); ?></p>
            <div style="background: #f8fafc; padding: 1rem; border-radius: 8px; margin-bottom: 1rem;">
                <p><strong><?php _e('Address:', 'iptv-pro'); ?></strong><br><code>${data.crypto_address}</code></p>
                <p><strong><?php _e('Amount:', 'iptv-pro'); ?></strong><br>$${data.crypto_amount}</p>
            </div>
            <p style="font-size: 0.875rem; color: #6b7280; margin-bottom: 1rem;">
                <?php _e('Your subscription will be activated once the payment is confirmed.', 'iptv-pro'); ?>
            </p>
            <button onclick="this.closest('div').parentElement.remove()" class="btn btn-primary">
                <?php _e('Close', 'iptv-pro'); ?>
            </button>
        </div>
    `;
    
    document.body.appendChild(modal);
}
</script>

<style>
@media (max-width: 768px) {
    .container > div {
        grid-template-columns: 1fr !important;
    }
    
    .order-summary {
        order: -1;
    }
    
    .order-summary > div {
        position: static !important;
    }
}
</style>

<?php get_footer(); ?>
