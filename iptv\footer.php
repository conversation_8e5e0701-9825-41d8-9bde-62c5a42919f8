<?php
/**
 * The template for displaying the footer
 *
 * @package IPTV_Pro
 */
?>

    <footer id="colophon" class="site-footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3><?php _e('About Us', 'iptv-pro'); ?></h3>
                    <p><?php echo get_theme_mod('footer_about', __('We provide premium IPTV services with thousands of channels, reliable streaming, and excellent customer support. Your entertainment is our priority.', 'iptv-pro')); ?></p>
                    <div class="social-links" style="margin-top: 1rem;">
                        <?php if (get_theme_mod('facebook_url')) : ?>
                            <a href="<?php echo esc_url(get_theme_mod('facebook_url')); ?>" target="_blank" style="color: #d1d5db; margin-right: 1rem; font-size: 1.25rem;">📘</a>
                        <?php endif; ?>
                        <?php if (get_theme_mod('twitter_url')) : ?>
                            <a href="<?php echo esc_url(get_theme_mod('twitter_url')); ?>" target="_blank" style="color: #d1d5db; margin-right: 1rem; font-size: 1.25rem;">🐦</a>
                        <?php endif; ?>
                        <?php if (get_theme_mod('instagram_url')) : ?>
                            <a href="<?php echo esc_url(get_theme_mod('instagram_url')); ?>" target="_blank" style="color: #d1d5db; margin-right: 1rem; font-size: 1.25rem;">📷</a>
                        <?php endif; ?>
                        <?php if (get_theme_mod('telegram_url')) : ?>
                            <a href="<?php echo esc_url(get_theme_mod('telegram_url')); ?>" target="_blank" style="color: #d1d5db; margin-right: 1rem; font-size: 1.25rem;">✈️</a>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="footer-section">
                    <h3><?php _e('Quick Links', 'iptv-pro'); ?></h3>
                    <ul>
                        <li><a href="<?php echo get_permalink(get_page_by_path('packages')); ?>"><?php _e('IPTV Packages', 'iptv-pro'); ?></a></li>
                        <li><a href="<?php echo get_permalink(get_page_by_path('channels')); ?>"><?php _e('Channel List', 'iptv-pro'); ?></a></li>
                        <li><a href="<?php echo get_permalink(get_page_by_path('free-trial')); ?>"><?php _e('Free Trial', 'iptv-pro'); ?></a></li>
                        <li><a href="<?php echo get_permalink(get_page_by_path('reseller')); ?>"><?php _e('Become a Reseller', 'iptv-pro'); ?></a></li>
                        <li><a href="<?php echo get_permalink(get_page_by_path('apps')); ?>"><?php _e('Download Apps', 'iptv-pro'); ?></a></li>
                    </ul>
                </div>

                <div class="footer-section">
                    <h3><?php _e('Support', 'iptv-pro'); ?></h3>
                    <ul>
                        <li><a href="<?php echo get_permalink(get_page_by_path('support')); ?>"><?php _e('Help Center', 'iptv-pro'); ?></a></li>
                        <li><a href="<?php echo get_permalink(get_page_by_path('setup-guide')); ?>"><?php _e('Setup Guide', 'iptv-pro'); ?></a></li>
                        <li><a href="<?php echo get_permalink(get_page_by_path('faq')); ?>"><?php _e('FAQ', 'iptv-pro'); ?></a></li>
                        <li><a href="<?php echo get_permalink(get_page_by_path('contact')); ?>"><?php _e('Contact Us', 'iptv-pro'); ?></a></li>
                        <li><a href="<?php echo get_permalink(get_page_by_path('ticket')); ?>"><?php _e('Submit Ticket', 'iptv-pro'); ?></a></li>
                    </ul>
                </div>

                <div class="footer-section">
                    <h3><?php _e('Contact Info', 'iptv-pro'); ?></h3>
                    <div class="contact-info">
                        <?php if (get_theme_mod('contact_email')) : ?>
                            <p style="margin-bottom: 0.5rem;">
                                <strong><?php _e('Email:', 'iptv-pro'); ?></strong><br>
                                <a href="mailto:<?php echo esc_attr(get_theme_mod('contact_email')); ?>" style="color: #d1d5db;">
                                    <?php echo esc_html(get_theme_mod('contact_email')); ?>
                                </a>
                            </p>
                        <?php endif; ?>
                        
                        <?php if (get_theme_mod('contact_phone')) : ?>
                            <p style="margin-bottom: 0.5rem;">
                                <strong><?php _e('Phone:', 'iptv-pro'); ?></strong><br>
                                <a href="tel:<?php echo esc_attr(get_theme_mod('contact_phone')); ?>" style="color: #d1d5db;">
                                    <?php echo esc_html(get_theme_mod('contact_phone')); ?>
                                </a>
                            </p>
                        <?php endif; ?>
                        
                        <?php if (get_theme_mod('whatsapp_number')) : ?>
                            <p style="margin-bottom: 0.5rem;">
                                <strong><?php _e('WhatsApp:', 'iptv-pro'); ?></strong><br>
                                <a href="https://wa.me/<?php echo esc_attr(str_replace('+', '', get_theme_mod('whatsapp_number'))); ?>" target="_blank" style="color: #d1d5db;">
                                    <?php echo esc_html(get_theme_mod('whatsapp_number')); ?>
                                </a>
                            </p>
                        <?php endif; ?>
                        
                        <p style="margin-bottom: 0.5rem;">
                            <strong><?php _e('Support Hours:', 'iptv-pro'); ?></strong><br>
                            <?php echo get_theme_mod('support_hours', __('24/7 Available', 'iptv-pro')); ?>
                        </p>
                    </div>
                </div>
            </div>

            <div class="footer-bottom">
                <div style="display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; gap: 1rem;">
                    <div>
                        <p>&copy; <?php echo date('Y'); ?> <?php bloginfo('name'); ?>. <?php _e('All rights reserved.', 'iptv-pro'); ?></p>
                    </div>
                    <div class="footer-links">
                        <a href="<?php echo get_permalink(get_page_by_path('privacy-policy')); ?>" style="color: #9ca3af; margin-right: 1rem;"><?php _e('Privacy Policy', 'iptv-pro'); ?></a>
                        <a href="<?php echo get_permalink(get_page_by_path('terms-of-service')); ?>" style="color: #9ca3af; margin-right: 1rem;"><?php _e('Terms of Service', 'iptv-pro'); ?></a>
                        <a href="<?php echo get_permalink(get_page_by_path('refund-policy')); ?>" style="color: #9ca3af;"><?php _e('Refund Policy', 'iptv-pro'); ?></a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Live Chat Widget -->
    <?php if (get_theme_mod('enable_live_chat', true)) : ?>
        <div id="live-chat-widget" style="position: fixed; bottom: 20px; right: 20px; z-index: 1000;">
            <button id="chat-toggle" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border: none; border-radius: 50%; width: 60px; height: 60px; font-size: 1.5rem; cursor: pointer; box-shadow: 0 4px 12px rgba(0,0,0,0.15); transition: transform 0.3s;">
                💬
            </button>
            <div id="chat-window" style="display: none; position: absolute; bottom: 70px; right: 0; width: 300px; height: 400px; background: white; border-radius: 12px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); border: 1px solid #e5e7eb;">
                <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 1rem; border-radius: 12px 12px 0 0; display: flex; justify-content: space-between; align-items: center;">
                    <h4 style="margin: 0; font-size: 1rem;"><?php _e('Live Support', 'iptv-pro'); ?></h4>
                    <button id="chat-close" style="background: none; border: none; color: white; font-size: 1.25rem; cursor: pointer;">×</button>
                </div>
                <div style="padding: 1rem; height: calc(100% - 120px); overflow-y: auto;">
                    <p style="margin: 0 0 1rem 0; color: #6b7280; font-size: 0.875rem;"><?php _e('Hello! How can we help you today?', 'iptv-pro'); ?></p>
                    <div style="display: flex; flex-direction: column; gap: 0.5rem;">
                        <a href="<?php echo get_permalink(get_page_by_path('support')); ?>" style="background: #f3f4f6; padding: 0.75rem; border-radius: 8px; text-decoration: none; color: #374151; font-size: 0.875rem; transition: background 0.3s;">
                            <?php _e('📋 General Support', 'iptv-pro'); ?>
                        </a>
                        <a href="<?php echo get_permalink(get_page_by_path('setup-guide')); ?>" style="background: #f3f4f6; padding: 0.75rem; border-radius: 8px; text-decoration: none; color: #374151; font-size: 0.875rem; transition: background 0.3s;">
                            <?php _e('⚙️ Setup Help', 'iptv-pro'); ?>
                        </a>
                        <a href="<?php echo get_permalink(get_page_by_path('ticket')); ?>" style="background: #f3f4f6; padding: 0.75rem; border-radius: 8px; text-decoration: none; color: #374151; font-size: 0.875rem; transition: background 0.3s;">
                            <?php _e('🎫 Submit Ticket', 'iptv-pro'); ?>
                        </a>
                        <?php if (get_theme_mod('whatsapp_number')) : ?>
                            <a href="https://wa.me/<?php echo esc_attr(str_replace('+', '', get_theme_mod('whatsapp_number'))); ?>" target="_blank" style="background: #25d366; color: white; padding: 0.75rem; border-radius: 8px; text-decoration: none; font-size: 0.875rem; text-align: center; transition: background 0.3s;">
                                <?php _e('💬 WhatsApp Chat', 'iptv-pro'); ?>
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <!-- Back to Top Button -->
    <button id="back-to-top" style="display: none; position: fixed; bottom: 20px; left: 20px; background: #3b82f6; color: white; border: none; border-radius: 50%; width: 50px; height: 50px; font-size: 1.25rem; cursor: pointer; z-index: 1000; transition: all 0.3s;">
        ↑
    </button>

</div><!-- #page -->

<?php wp_footer(); ?>

<script>
// Mobile menu toggle
document.addEventListener('DOMContentLoaded', function() {
    const mobileToggle = document.getElementById('mobile-menu-toggle');
    const mobileNav = document.getElementById('mobile-navigation');
    
    if (mobileToggle && mobileNav) {
        mobileToggle.addEventListener('click', function() {
            if (mobileNav.style.display === 'none' || mobileNav.style.display === '') {
                mobileNav.style.display = 'block';
            } else {
                mobileNav.style.display = 'none';
            }
        });
    }
    
    // Live chat toggle
    const chatToggle = document.getElementById('chat-toggle');
    const chatWindow = document.getElementById('chat-window');
    const chatClose = document.getElementById('chat-close');
    
    if (chatToggle && chatWindow) {
        chatToggle.addEventListener('click', function() {
            if (chatWindow.style.display === 'none' || chatWindow.style.display === '') {
                chatWindow.style.display = 'block';
            } else {
                chatWindow.style.display = 'none';
            }
        });
    }
    
    if (chatClose && chatWindow) {
        chatClose.addEventListener('click', function() {
            chatWindow.style.display = 'none';
        });
    }
    
    // Back to top button
    const backToTop = document.getElementById('back-to-top');
    
    if (backToTop) {
        window.addEventListener('scroll', function() {
            if (window.pageYOffset > 300) {
                backToTop.style.display = 'block';
            } else {
                backToTop.style.display = 'none';
            }
        });
        
        backToTop.addEventListener('click', function() {
            window.scrollTo({ top: 0, behavior: 'smooth' });
        });
    }
    
    // Responsive menu handling
    function handleResize() {
        if (window.innerWidth > 768) {
            if (mobileNav) mobileNav.style.display = 'none';
            if (mobileToggle) mobileToggle.style.display = 'none';
        } else {
            if (mobileToggle) mobileToggle.style.display = 'block';
        }
    }
    
    window.addEventListener('resize', handleResize);
    handleResize(); // Call on load
});
</script>

</body>
</html>
