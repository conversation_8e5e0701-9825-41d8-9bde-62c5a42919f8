<?php
/**
 * Template Name: Register Page
 *
 * @package IPTV_Pro
 */

// Redirect if already logged in
if (is_user_logged_in()) {
    wp_redirect(get_permalink(get_page_by_path('dashboard')));
    exit;
}

// Check if registration is enabled
if (!get_option('users_can_register')) {
    wp_redirect(home_url());
    exit;
}

get_header(); ?>

<main class="main-content register-page">
    <div class="container" style="padding: 2rem 0;">
        <div style="max-width: 500px; margin: 0 auto;">
            
            <!-- Register Header -->
            <div style="text-align: center; margin-bottom: 2rem;">
                <h1 style="font-size: 2rem; font-weight: 700; margin-bottom: 0.5rem; color: #1f2937;">
                    <?php _e('Create Your Account', 'iptv-pro'); ?>
                </h1>
                <p style="color: #6b7280;">
                    <?php _e('Join thousands of satisfied customers enjoying premium IPTV', 'iptv-pro'); ?>
                </p>
            </div>

            <!-- Registration Form -->
            <div style="background: white; padding: 2rem; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.05);">
                
                <?php
                // Display registration errors
                if (isset($_GET['registration']) && $_GET['registration'] === 'failed') {
                    echo '<div style="background: #fef2f2; border: 1px solid #fecaca; color: #dc2626; padding: 1rem; border-radius: 6px; margin-bottom: 1rem; text-align: center;">';
                    echo __('Registration failed. Please check your information and try again.', 'iptv-pro');
                    echo '</div>';
                }
                
                if (isset($_GET['registration']) && $_GET['registration'] === 'disabled') {
                    echo '<div style="background: #fef2f2; border: 1px solid #fecaca; color: #dc2626; padding: 1rem; border-radius: 6px; margin-bottom: 1rem; text-align: center;">';
                    echo __('User registration is currently disabled.', 'iptv-pro');
                    echo '</div>';
                }
                ?>

                <form name="registerform" id="registerform" action="<?php echo esc_url(site_url('wp-login.php?action=register', 'login_post')); ?>" method="post" novalidate="novalidate">
                    
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; margin-bottom: 1rem;">
                        <div>
                            <label for="first_name" style="display: block; font-weight: 500; margin-bottom: 0.5rem; color: #374151;">
                                <?php _e('First Name', 'iptv-pro'); ?> <span style="color: #ef4444;">*</span>
                            </label>
                            <input type="text" name="first_name" id="first_name" class="input" value="<?php echo esc_attr(wp_unslash($_POST['first_name'] ?? '')); ?>" size="25" required style="width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 6px;" />
                        </div>
                        
                        <div>
                            <label for="last_name" style="display: block; font-weight: 500; margin-bottom: 0.5rem; color: #374151;">
                                <?php _e('Last Name', 'iptv-pro'); ?> <span style="color: #ef4444;">*</span>
                            </label>
                            <input type="text" name="last_name" id="last_name" class="input" value="<?php echo esc_attr(wp_unslash($_POST['last_name'] ?? '')); ?>" size="25" required style="width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 6px;" />
                        </div>
                    </div>
                    
                    <div style="margin-bottom: 1rem;">
                        <label for="user_login" style="display: block; font-weight: 500; margin-bottom: 0.5rem; color: #374151;">
                            <?php _e('Username', 'iptv-pro'); ?> <span style="color: #ef4444;">*</span>
                        </label>
                        <input type="text" name="user_login" id="user_login" class="input" value="<?php echo esc_attr(wp_unslash($_POST['user_login'] ?? '')); ?>" size="20" autocapitalize="off" required style="width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 6px;" />
                        <small style="color: #6b7280; font-size: 0.75rem;"><?php _e('Username cannot be changed later', 'iptv-pro'); ?></small>
                    </div>
                    
                    <div style="margin-bottom: 1rem;">
                        <label for="user_email" style="display: block; font-weight: 500; margin-bottom: 0.5rem; color: #374151;">
                            <?php _e('Email Address', 'iptv-pro'); ?> <span style="color: #ef4444;">*</span>
                        </label>
                        <input type="email" name="user_email" id="user_email" class="input" value="<?php echo esc_attr(wp_unslash($_POST['user_email'] ?? '')); ?>" size="25" required style="width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 6px;" />
                    </div>
                    
                    <div style="margin-bottom: 1rem;">
                        <label for="phone" style="display: block; font-weight: 500; margin-bottom: 0.5rem; color: #374151;">
                            <?php _e('Phone Number', 'iptv-pro'); ?>
                        </label>
                        <input type="tel" name="phone" id="phone" class="input" value="<?php echo esc_attr(wp_unslash($_POST['phone'] ?? '')); ?>" size="25" style="width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 6px;" />
                    </div>
                    
                    <div style="margin-bottom: 1rem;">
                        <label for="country" style="display: block; font-weight: 500; margin-bottom: 0.5rem; color: #374151;">
                            <?php _e('Country', 'iptv-pro'); ?>
                        </label>
                        <select name="country" id="country" class="input" style="width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 6px;">
                            <option value=""><?php _e('Select Country', 'iptv-pro'); ?></option>
                            <option value="US"><?php _e('United States', 'iptv-pro'); ?></option>
                            <option value="CA"><?php _e('Canada', 'iptv-pro'); ?></option>
                            <option value="UK"><?php _e('United Kingdom', 'iptv-pro'); ?></option>
                            <option value="DE"><?php _e('Germany', 'iptv-pro'); ?></option>
                            <option value="FR"><?php _e('France', 'iptv-pro'); ?></option>
                            <option value="ES"><?php _e('Spain', 'iptv-pro'); ?></option>
                            <option value="IT"><?php _e('Italy', 'iptv-pro'); ?></option>
                            <option value="AU"><?php _e('Australia', 'iptv-pro'); ?></option>
                            <option value="NL"><?php _e('Netherlands', 'iptv-pro'); ?></option>
                            <option value="BE"><?php _e('Belgium', 'iptv-pro'); ?></option>
                            <option value="CH"><?php _e('Switzerland', 'iptv-pro'); ?></option>
                            <option value="AT"><?php _e('Austria', 'iptv-pro'); ?></option>
                            <option value="SE"><?php _e('Sweden', 'iptv-pro'); ?></option>
                            <option value="NO"><?php _e('Norway', 'iptv-pro'); ?></option>
                            <option value="DK"><?php _e('Denmark', 'iptv-pro'); ?></option>
                            <option value="FI"><?php _e('Finland', 'iptv-pro'); ?></option>
                        </select>
                    </div>
                    
                    <!-- Password will be auto-generated and sent via email -->
                    <div style="background: #f0f9ff; border: 1px solid #bae6fd; padding: 1rem; border-radius: 6px; margin-bottom: 1.5rem;">
                        <div style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 0.5rem;">
                            <span style="color: #0369a1; font-size: 1.25rem;">🔒</span>
                            <span style="font-weight: 600; color: #0369a1; font-size: 0.875rem;"><?php _e('Password Information', 'iptv-pro'); ?></span>
                        </div>
                        <p style="color: #0369a1; font-size: 0.875rem; margin: 0;">
                            <?php _e('A secure password will be automatically generated and sent to your email address.', 'iptv-pro'); ?>
                        </p>
                    </div>
                    
                    <!-- Terms and Conditions -->
                    <div style="margin-bottom: 1.5rem;">
                        <label style="display: flex; align-items: start; gap: 0.75rem; cursor: pointer;">
                            <input type="checkbox" name="terms_accepted" required style="margin-top: 0.25rem;">
                            <span style="color: #374151; font-size: 0.875rem;">
                                <?php printf(
                                    __('I agree to the %s and %s', 'iptv-pro'),
                                    '<a href="' . get_permalink(get_page_by_path('terms-of-service')) . '" target="_blank" style="color: #3b82f6;">' . __('Terms of Service', 'iptv-pro') . '</a>',
                                    '<a href="' . get_permalink(get_page_by_path('privacy-policy')) . '" target="_blank" style="color: #3b82f6;">' . __('Privacy Policy', 'iptv-pro') . '</a>'
                                ); ?>
                            </span>
                        </label>
                    </div>
                    
                    <!-- Newsletter Subscription -->
                    <div style="margin-bottom: 1.5rem;">
                        <label style="display: flex; align-items: start; gap: 0.75rem; cursor: pointer;">
                            <input type="checkbox" name="newsletter_subscribe" value="1" checked style="margin-top: 0.25rem;">
                            <span style="color: #374151; font-size: 0.875rem;">
                                <?php _e('Subscribe to our newsletter for updates and special offers', 'iptv-pro'); ?>
                            </span>
                        </label>
                    </div>
                    
                    <button type="submit" name="wp-submit" id="wp-submit" class="btn btn-primary" style="width: 100%; font-size: 1rem; padding: 0.75rem;">
                        <?php _e('Create Account', 'iptv-pro'); ?>
                    </button>
                    
                    <input type="hidden" name="redirect_to" value="<?php echo esc_attr($_REQUEST['redirect_to'] ?? get_permalink(get_page_by_path('login')) . '?registration=complete'); ?>" />
                </form>
                
                <!-- Benefits Section -->
                <div style="margin-top: 2rem; padding-top: 2rem; border-top: 1px solid #e5e7eb;">
                    <h3 style="font-size: 1rem; font-weight: 600; margin-bottom: 1rem; color: #374151; text-align: center;">
                        <?php _e('Why Choose Our IPTV Service?', 'iptv-pro'); ?>
                    </h3>
                    <div style="display: grid; gap: 0.75rem;">
                        <div style="display: flex; align-items: center; gap: 0.75rem;">
                            <span style="color: #10b981; font-weight: bold;">✓</span>
                            <span style="color: #6b7280; font-size: 0.875rem;"><?php _e('24/7 Premium Support', 'iptv-pro'); ?></span>
                        </div>
                        <div style="display: flex; align-items: center; gap: 0.75rem;">
                            <span style="color: #10b981; font-weight: bold;">✓</span>
                            <span style="color: #6b7280; font-size: 0.875rem;"><?php _e('HD & 4K Quality Channels', 'iptv-pro'); ?></span>
                        </div>
                        <div style="display: flex; align-items: center; gap: 0.75rem;">
                            <span style="color: #10b981; font-weight: bold;">✓</span>
                            <span style="color: #6b7280; font-size: 0.875rem;"><?php _e('Multi-Device Compatibility', 'iptv-pro'); ?></span>
                        </div>
                        <div style="display: flex; align-items: center; gap: 0.75rem;">
                            <span style="color: #10b981; font-weight: bold;">✓</span>
                            <span style="color: #6b7280; font-size: 0.875rem;"><?php _e('No Buffering Guarantee', 'iptv-pro'); ?></span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Login Link -->
            <div style="text-align: center; margin-top: 2rem;">
                <p style="color: #6b7280;">
                    <?php _e('Already have an account?', 'iptv-pro'); ?>
                    <a href="<?php echo get_permalink(get_page_by_path('login')); ?>" style="color: #3b82f6; text-decoration: none; font-weight: 500;">
                        <?php _e('Sign in here', 'iptv-pro'); ?>
                    </a>
                </p>
            </div>
        </div>
    </div>
</main>

<style>
.register-page {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: calc(100vh - 160px);
    display: flex;
    align-items: center;
}

.register-page .container {
    width: 100%;
}

@media (max-width: 768px) {
    .register-page .container > div {
        max-width: 100% !important;
        padding: 0 1rem;
    }
    
    .register-page .container > div > div {
        padding: 1.5rem !important;
    }
    
    .register-page form > div:first-child {
        grid-template-columns: 1fr !important;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Username validation
    const usernameInput = document.getElementById('user_login');
    usernameInput.addEventListener('input', function() {
        const username = this.value;
        const validUsername = /^[a-zA-Z0-9_-]+$/.test(username);
        
        if (username.length > 0 && !validUsername) {
            this.style.borderColor = '#ef4444';
            if (!document.getElementById('username-error')) {
                const error = document.createElement('small');
                error.id = 'username-error';
                error.style.color = '#ef4444';
                error.style.fontSize = '0.75rem';
                error.textContent = '<?php _e('Username can only contain letters, numbers, underscores, and hyphens', 'iptv-pro'); ?>';
                this.parentNode.appendChild(error);
            }
        } else {
            this.style.borderColor = '#d1d5db';
            const error = document.getElementById('username-error');
            if (error) {
                error.remove();
            }
        }
    });
    
    // Email validation
    const emailInput = document.getElementById('user_email');
    emailInput.addEventListener('blur', function() {
        const email = this.value;
        const validEmail = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
        
        if (email.length > 0 && !validEmail) {
            this.style.borderColor = '#ef4444';
        } else {
            this.style.borderColor = '#d1d5db';
        }
    });
    
    // Form submission validation
    const form = document.getElementById('registerform');
    form.addEventListener('submit', function(e) {
        const requiredFields = form.querySelectorAll('input[required]');
        let isValid = true;
        
        requiredFields.forEach(field => {
            if (!field.value.trim()) {
                field.style.borderColor = '#ef4444';
                isValid = false;
            } else {
                field.style.borderColor = '#d1d5db';
            }
        });
        
        if (!isValid) {
            e.preventDefault();
            alert('<?php _e('Please fill in all required fields.', 'iptv-pro'); ?>');
        }
    });
});
</script>

<?php get_footer(); ?>
