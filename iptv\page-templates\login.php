<?php
/**
 * Template Name: Login Page
 *
 * @package IPTV_Pro
 */

// Redirect if already logged in
if (is_user_logged_in()) {
    wp_redirect(get_permalink(get_page_by_path('dashboard')));
    exit;
}

get_header(); ?>

<main class="main-content login-page">
    <div class="container" style="padding: 2rem 0;">
        <div style="max-width: 400px; margin: 0 auto;">
            
            <!-- <PERSON>gin Header -->
            <div style="text-align: center; margin-bottom: 2rem;">
                <h1 style="font-size: 2rem; font-weight: 700; margin-bottom: 0.5rem; color: #1f2937;">
                    <?php _e('Welcome Back', 'iptv-pro'); ?>
                </h1>
                <p style="color: #6b7280;">
                    <?php _e('Sign in to access your IPTV dashboard', 'iptv-pro'); ?>
                </p>
            </div>

            <!-- Login Form -->
            <div style="background: white; padding: 2rem; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.05);">
                
                <?php
                // Display login errors
                if (isset($_GET['login']) && $_GET['login'] === 'failed') {
                    echo '<div style="background: #fef2f2; border: 1px solid #fecaca; color: #dc2626; padding: 1rem; border-radius: 6px; margin-bottom: 1rem; text-align: center;">';
                    echo __('Invalid username or password. Please try again.', 'iptv-pro');
                    echo '</div>';
                }
                
                if (isset($_GET['login']) && $_GET['login'] === 'empty') {
                    echo '<div style="background: #fef2f2; border: 1px solid #fecaca; color: #dc2626; padding: 1rem; border-radius: 6px; margin-bottom: 1rem; text-align: center;">';
                    echo __('Please enter your username and password.', 'iptv-pro');
                    echo '</div>';
                }
                
                if (isset($_GET['registration']) && $_GET['registration'] === 'complete') {
                    echo '<div style="background: #f0f9ff; border: 1px solid #bae6fd; color: #0369a1; padding: 1rem; border-radius: 6px; margin-bottom: 1rem; text-align: center;">';
                    echo __('Registration successful! Please log in with your credentials.', 'iptv-pro');
                    echo '</div>';
                }
                ?>

                <form name="loginform" id="loginform" action="<?php echo esc_url(site_url('wp-login.php', 'login_post')); ?>" method="post">
                    <div style="margin-bottom: 1rem;">
                        <label for="user_login" style="display: block; font-weight: 500; margin-bottom: 0.5rem; color: #374151;">
                            <?php _e('Username or Email', 'iptv-pro'); ?>
                        </label>
                        <input type="text" name="log" id="user_login" class="input" value="<?php echo esc_attr($user_login ?? ''); ?>" size="20" autocapitalize="off" required style="width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 6px; font-size: 1rem;" />
                    </div>
                    
                    <div style="margin-bottom: 1rem;">
                        <label for="user_pass" style="display: block; font-weight: 500; margin-bottom: 0.5rem; color: #374151;">
                            <?php _e('Password', 'iptv-pro'); ?>
                        </label>
                        <input type="password" name="pwd" id="user_pass" class="input" value="" size="20" required style="width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 6px; font-size: 1rem;" />
                    </div>
                    
                    <div style="margin-bottom: 1.5rem;">
                        <label style="display: flex; align-items: center; gap: 0.5rem; cursor: pointer;">
                            <input name="rememberme" type="checkbox" id="rememberme" value="forever" />
                            <span style="color: #374151; font-size: 0.875rem;"><?php _e('Remember Me', 'iptv-pro'); ?></span>
                        </label>
                    </div>
                    
                    <button type="submit" name="wp-submit" id="wp-submit" class="btn btn-primary" style="width: 100%; font-size: 1rem; padding: 0.75rem;">
                        <?php _e('Sign In', 'iptv-pro'); ?>
                    </button>
                    
                    <input type="hidden" name="redirect_to" value="<?php echo esc_attr($_REQUEST['redirect_to'] ?? get_permalink(get_page_by_path('dashboard'))); ?>" />
                </form>
                
                <!-- Additional Links -->
                <div style="margin-top: 1.5rem; text-align: center;">
                    <a href="<?php echo wp_lostpassword_url(); ?>" style="color: #3b82f6; text-decoration: none; font-size: 0.875rem;">
                        <?php _e('Forgot your password?', 'iptv-pro'); ?>
                    </a>
                </div>
            </div>
            
            <!-- Register Link -->
            <div style="text-align: center; margin-top: 2rem;">
                <p style="color: #6b7280;">
                    <?php _e('Don\'t have an account?', 'iptv-pro'); ?>
                    <a href="<?php echo get_permalink(get_page_by_path('register')); ?>" style="color: #3b82f6; text-decoration: none; font-weight: 500;">
                        <?php _e('Sign up here', 'iptv-pro'); ?>
                    </a>
                </p>
            </div>
            
            <!-- Social Login (if available) -->
            <div style="margin-top: 2rem;">
                <div style="text-align: center; margin-bottom: 1rem;">
                    <span style="color: #6b7280; font-size: 0.875rem; background: white; padding: 0 1rem; position: relative;">
                        <?php _e('Or continue with', 'iptv-pro'); ?>
                    </span>
                    <div style="border-top: 1px solid #e5e7eb; position: absolute; width: 100%; top: 50%; left: 0; z-index: -1;"></div>
                </div>
                
                <!-- Add social login buttons here if needed -->
                <div style="display: grid; gap: 0.75rem;">
                    <!-- Example social login buttons -->
                    <!--
                    <button class="btn btn-secondary" style="display: flex; align-items: center; justify-content: center; gap: 0.5rem;">
                        <span>📧</span> Continue with Google
                    </button>
                    <button class="btn btn-secondary" style="display: flex; align-items: center; justify-content: center; gap: 0.5rem;">
                        <span>📘</span> Continue with Facebook
                    </button>
                    -->
                </div>
            </div>
        </div>
    </div>
</main>

<style>
.login-page {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: calc(100vh - 160px);
    display: flex;
    align-items: center;
}

.login-page .container {
    width: 100%;
}

@media (max-width: 480px) {
    .login-page .container > div {
        max-width: 100% !important;
        padding: 0 1rem;
    }
    
    .login-page .container > div > div {
        padding: 1.5rem !important;
    }
}
</style>

<?php get_footer(); ?>
