<?php
/**
 * Template Name: Package Comparison
 *
 * @package IPTV_Pro
 */

get_header(); ?>

<main class="main-content package-comparison-page">
    <div class="container" style="padding: 2rem 0;">
        
        <!-- Comparison Header -->
        <div style="text-align: center; margin-bottom: 3rem;">
            <h1 style="font-size: 3rem; font-weight: 700; margin-bottom: 1rem; color: #1f2937;">
                <?php _e('Compare IPTV Packages', 'iptv-pro'); ?>
            </h1>
            <p style="font-size: 1.25rem; color: #6b7280; max-width: 600px; margin: 0 auto;">
                <?php _e('Find the perfect IPTV package for your needs. Compare features, channels, and pricing side by side.', 'iptv-pro'); ?>
            </p>
        </div>

        <!-- Package Selection -->
        <div style="background: white; padding: 2rem; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.05); margin-bottom: 3rem;">
            <h2 style="font-size: 1.5rem; font-weight: 600; margin-bottom: 1.5rem; color: #1f2937;">
                <?php _e('Select Packages to Compare', 'iptv-pro'); ?>
            </h2>
            
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                <?php
                $packages = get_posts(array(
                    'post_type' => 'iptv_package',
                    'posts_per_page' => -1,
                    'post_status' => 'publish'
                ));
                
                foreach ($packages as $package) :
                    $price = get_post_meta($package->ID, '_iptv_price', true);
                ?>
                    <label style="display: flex; align-items: center; padding: 1rem; border: 2px solid #e5e7eb; border-radius: 8px; cursor: pointer; transition: all 0.3s;">
                        <input type="checkbox" class="compare-package" value="<?php echo $package->ID; ?>" style="margin-right: 1rem;">
                        <div>
                            <div style="font-weight: 600; color: #1f2937;"><?php echo $package->post_title; ?></div>
                            <div style="color: #3b82f6; font-weight: 600;">$<?php echo $price; ?></div>
                        </div>
                    </label>
                <?php endforeach; ?>
            </div>
            
            <div style="text-align: center; margin-top: 1.5rem;">
                <button id="compare-selected" class="btn btn-primary" disabled>
                    <?php _e('Compare Selected Packages', 'iptv-pro'); ?>
                </button>
            </div>
        </div>

        <!-- Comparison Table -->
        <div id="comparison-table" style="display: none;">
            <div class="comparison-table">
                <table style="width: 100%; border-collapse: collapse;">
                    <thead style="background: #f8fafc;">
                        <tr>
                            <th style="padding: 1rem; text-align: left; font-weight: 600; color: #374151; border-bottom: 2px solid #e5e7eb;">
                                <?php _e('Features', 'iptv-pro'); ?>
                            </th>
                            <!-- Package columns will be added dynamically -->
                        </tr>
                    </thead>
                    <tbody id="comparison-body">
                        <!-- Comparison rows will be added dynamically -->
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Quick Comparison Guide -->
        <div style="background: white; padding: 2rem; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.05); margin-top: 3rem;">
            <h2 style="font-size: 1.5rem; font-weight: 600; margin-bottom: 1.5rem; color: #1f2937;">
                <?php _e('How to Choose the Right Package', 'iptv-pro'); ?>
            </h2>
            
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 2rem;">
                <div>
                    <h3 style="font-size: 1.125rem; font-weight: 600; margin-bottom: 1rem; color: #374151;">
                        🏠 <?php _e('For Families', 'iptv-pro'); ?>
                    </h3>
                    <ul style="color: #6b7280; line-height: 1.6;">
                        <li>• <?php _e('Multiple connections (2-5)', 'iptv-pro'); ?></li>
                        <li>• <?php _e('Kids channels included', 'iptv-pro'); ?></li>
                        <li>• <?php _e('Parental controls', 'iptv-pro'); ?></li>
                        <li>• <?php _e('VOD library access', 'iptv-pro'); ?></li>
                    </ul>
                </div>
                
                <div>
                    <h3 style="font-size: 1.125rem; font-weight: 600; margin-bottom: 1rem; color: #374151;">
                        ⚽ <?php _e('For Sports Fans', 'iptv-pro'); ?>
                    </h3>
                    <ul style="color: #6b7280; line-height: 1.6;">
                        <li>• <?php _e('Premium sports channels', 'iptv-pro'); ?></li>
                        <li>• <?php _e('Live events coverage', 'iptv-pro'); ?></li>
                        <li>• <?php _e('Multiple camera angles', 'iptv-pro'); ?></li>
                        <li>• <?php _e('Sports replay features', 'iptv-pro'); ?></li>
                    </ul>
                </div>
                
                <div>
                    <h3 style="font-size: 1.125rem; font-weight: 600; margin-bottom: 1rem; color: #374151;">
                        🎬 <?php _e('For Movie Lovers', 'iptv-pro'); ?>
                    </h3>
                    <ul style="color: #6b7280; line-height: 1.6;">
                        <li>• <?php _e('Premium movie channels', 'iptv-pro'); ?></li>
                        <li>• <?php _e('Latest releases', 'iptv-pro'); ?></li>
                        <li>• <?php _e('4K/HD quality', 'iptv-pro'); ?></li>
                        <li>• <?php _e('Extensive VOD library', 'iptv-pro'); ?></li>
                    </ul>
                </div>
                
                <div>
                    <h3 style="font-size: 1.125rem; font-weight: 600; margin-bottom: 1rem; color: #374151;">
                        💰 <?php _e('Budget Conscious', 'iptv-pro'); ?>
                    </h3>
                    <ul style="color: #6b7280; line-height: 1.6;">
                        <li>• <?php _e('Essential channels only', 'iptv-pro'); ?></li>
                        <li>• <?php _e('Single connection', 'iptv-pro'); ?></li>
                        <li>• <?php _e('Basic quality (HD)', 'iptv-pro'); ?></li>
                        <li>• <?php _e('Monthly payment option', 'iptv-pro'); ?></li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- FAQ Section -->
        <div style="background: white; padding: 2rem; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.05); margin-top: 3rem;">
            <h2 style="font-size: 1.5rem; font-weight: 600; margin-bottom: 1.5rem; color: #1f2937;">
                <?php _e('Comparison FAQ', 'iptv-pro'); ?>
            </h2>
            
            <div class="faq-container">
                <div class="faq-item" style="border-bottom: 1px solid #e5e7eb; padding: 1rem 0;">
                    <button class="faq-question" style="width: 100%; text-align: left; background: none; border: none; font-weight: 600; color: #1f2937; cursor: pointer; display: flex; justify-content: space-between; align-items: center;">
                        <?php _e('Can I upgrade or downgrade my package later?', 'iptv-pro'); ?>
                        <span class="faq-toggle">+</span>
                    </button>
                    <div class="faq-answer" style="display: none; padding-top: 1rem; color: #6b7280;">
                        <p><?php _e('Yes, you can change your package at any time from your customer dashboard. Changes take effect immediately, and billing is prorated.', 'iptv-pro'); ?></p>
                    </div>
                </div>
                
                <div class="faq-item" style="border-bottom: 1px solid #e5e7eb; padding: 1rem 0;">
                    <button class="faq-question" style="width: 100%; text-align: left; background: none; border: none; font-weight: 600; color: #1f2937; cursor: pointer; display: flex; justify-content: space-between; align-items: center;">
                        <?php _e('What\'s the difference between HD and 4K quality?', 'iptv-pro'); ?>
                        <span class="faq-toggle">+</span>
                    </button>
                    <div class="faq-answer" style="display: none; padding-top: 1rem; color: #6b7280;">
                        <p><?php _e('HD provides 1080p resolution, while 4K offers four times the detail with 2160p resolution. 4K requires faster internet (25+ Mbps) and compatible devices.', 'iptv-pro'); ?></p>
                    </div>
                </div>
                
                <div class="faq-item" style="padding: 1rem 0;">
                    <button class="faq-question" style="width: 100%; text-align: left; background: none; border: none; font-weight: 600; color: #1f2937; cursor: pointer; display: flex; justify-content: space-between; align-items: center;">
                        <?php _e('Do all packages include the same apps compatibility?', 'iptv-pro'); ?>
                        <span class="faq-toggle">+</span>
                    </button>
                    <div class="faq-answer" style="display: none; padding-top: 1rem; color: #6b7280;">
                        <p><?php _e('Yes, all packages work with the same IPTV apps and devices. The difference is in channel count, quality, and additional features like VOD access.', 'iptv-pro'); ?></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const compareCheckboxes = document.querySelectorAll('.compare-package');
    const compareButton = document.getElementById('compare-selected');
    const comparisonTable = document.getElementById('comparison-table');
    
    // Handle package selection
    compareCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            const selectedPackages = document.querySelectorAll('.compare-package:checked');
            
            // Update button state
            compareButton.disabled = selectedPackages.length < 2;
            
            // Update label styling
            const label = this.closest('label');
            if (this.checked) {
                label.style.borderColor = '#3b82f6';
                label.style.backgroundColor = '#f0f9ff';
            } else {
                label.style.borderColor = '#e5e7eb';
                label.style.backgroundColor = 'white';
            }
            
            // Limit to 4 packages
            if (selectedPackages.length >= 4) {
                compareCheckboxes.forEach(cb => {
                    if (!cb.checked) {
                        cb.disabled = true;
                        cb.closest('label').style.opacity = '0.5';
                    }
                });
            } else {
                compareCheckboxes.forEach(cb => {
                    cb.disabled = false;
                    cb.closest('label').style.opacity = '1';
                });
            }
        });
    });
    
    // Handle comparison
    compareButton.addEventListener('click', function() {
        const selectedPackages = Array.from(document.querySelectorAll('.compare-package:checked')).map(cb => cb.value);
        
        if (selectedPackages.length >= 2) {
            generateComparisonTable(selectedPackages);
            comparisonTable.style.display = 'block';
            comparisonTable.scrollIntoView({ behavior: 'smooth' });
        }
    });
    
    // FAQ functionality
    const faqQuestions = document.querySelectorAll('.faq-question');
    faqQuestions.forEach(question => {
        question.addEventListener('click', function() {
            const answer = this.nextElementSibling;
            const toggle = this.querySelector('.faq-toggle');
            
            if (answer.style.display === 'none' || answer.style.display === '') {
                answer.style.display = 'block';
                toggle.textContent = '−';
            } else {
                answer.style.display = 'none';
                toggle.textContent = '+';
            }
        });
    });
});

function generateComparisonTable(packageIds) {
    // This would typically fetch package data via AJAX
    // For demo purposes, we'll use placeholder data
    
    const comparisonData = {
        'Price': {},
        'Duration': {},
        'Channels': {},
        'Quality': {},
        'Connections': {},
        'VOD Access': {},
        'Sports Channels': {},
        'Kids Channels': {},
        'Support': {}
    };
    
    // Populate with actual package data (would come from AJAX in real implementation)
    packageIds.forEach(id => {
        comparisonData['Price'][id] = '$' + (Math.random() * 50 + 10).toFixed(2);
        comparisonData['Duration'][id] = Math.floor(Math.random() * 12 + 1) + ' months';
        comparisonData['Channels'][id] = Math.floor(Math.random() * 5000 + 1000) + '+';
        comparisonData['Quality'][id] = Math.random() > 0.5 ? 'HD & 4K' : 'HD';
        comparisonData['Connections'][id] = Math.floor(Math.random() * 5 + 1);
        comparisonData['VOD Access'][id] = Math.random() > 0.3 ? '✓' : '✗';
        comparisonData['Sports Channels'][id] = Math.random() > 0.4 ? '✓' : '✗';
        comparisonData['Kids Channels'][id] = Math.random() > 0.5 ? '✓' : '✗';
        comparisonData['Support'][id] = '24/7';
    });
    
    // Generate table HTML
    const table = document.querySelector('#comparison-table table');
    const thead = table.querySelector('thead tr');
    const tbody = document.getElementById('comparison-body');
    
    // Clear existing content
    thead.innerHTML = '<th style="padding: 1rem; text-align: left; font-weight: 600; color: #374151; border-bottom: 2px solid #e5e7eb;">Features</th>';
    tbody.innerHTML = '';
    
    // Add package headers
    packageIds.forEach(id => {
        const packageName = document.querySelector(`input[value="${id}"]`).closest('label').textContent.trim();
        thead.innerHTML += `<th style="padding: 1rem; text-align: center; font-weight: 600; color: #374151; border-bottom: 2px solid #e5e7eb;">${packageName}</th>`;
    });
    
    // Add comparison rows
    Object.keys(comparisonData).forEach(feature => {
        const row = document.createElement('tr');
        row.innerHTML = `<td style="padding: 1rem; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb;">${feature}</td>`;
        
        packageIds.forEach(id => {
            const value = comparisonData[feature][id];
            const cellClass = value === '✓' ? 'color: #10b981; font-weight: bold;' : value === '✗' ? 'color: #ef4444; font-weight: bold;' : 'color: #1f2937;';
            row.innerHTML += `<td style="padding: 1rem; text-align: center; border-bottom: 1px solid #e5e7eb; ${cellClass}">${value}</td>`;
        });
        
        tbody.appendChild(row);
    });
    
    // Add action row
    const actionRow = document.createElement('tr');
    actionRow.innerHTML = '<td style="padding: 1rem; font-weight: 600; color: #374151;">Action</td>';
    
    packageIds.forEach(id => {
        actionRow.innerHTML += `<td style="padding: 1rem; text-align: center;"><a href="#" class="btn btn-primary" style="font-size: 0.875rem; padding: 0.5rem 1rem;">Order Now</a></td>`;
    });
    
    tbody.appendChild(actionRow);
}
</script>

<style>
@media (max-width: 768px) {
    .comparison-table {
        overflow-x: auto;
    }
    
    .comparison-table table {
        min-width: 600px;
    }
}
</style>

<?php get_footer(); ?>
