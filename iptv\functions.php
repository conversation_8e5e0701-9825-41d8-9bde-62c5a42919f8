<?php
/**
 * IPTV Pro Theme Functions
 * 
 * @package IPTV_Pro
 * @version 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Theme setup
function iptv_pro_setup() {
    // Add theme support
    add_theme_support('title-tag');
    add_theme_support('post-thumbnails');
    add_theme_support('html5', array(
        'search-form',
        'comment-form',
        'comment-list',
        'gallery',
        'caption',
    ));
    add_theme_support('custom-logo');
    add_theme_support('customize-selective-refresh-widgets');
    
    // Register navigation menus
    register_nav_menus(array(
        'primary' => __('Primary Menu', 'iptv-pro'),
        'footer' => __('Footer Menu', 'iptv-pro'),
    ));
    
    // Add image sizes
    add_image_size('iptv-featured', 800, 450, true);
    add_image_size('iptv-thumbnail', 300, 200, true);
}
add_action('after_setup_theme', 'iptv_pro_setup');

// Enqueue scripts and styles
function iptv_pro_scripts() {
    // Enqueue styles
    wp_enqueue_style('iptv-pro-style', get_stylesheet_uri(), array(), '1.0.0');
    wp_enqueue_style('iptv-pro-fonts', 'https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap', array(), null);
    
    // Enqueue scripts
    wp_enqueue_script('iptv-pro-main', get_template_directory_uri() . '/assets/js/main.js', array('jquery'), '1.0.0', true);
    
    // Localize script for AJAX
    wp_localize_script('iptv-pro-main', 'iptv_ajax', array(
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('iptv_nonce'),
    ));
}
add_action('wp_enqueue_scripts', 'iptv_pro_scripts');

// Register widget areas
function iptv_pro_widgets_init() {
    register_sidebar(array(
        'name'          => __('Sidebar', 'iptv-pro'),
        'id'            => 'sidebar-1',
        'description'   => __('Add widgets here.', 'iptv-pro'),
        'before_widget' => '<section id="%1$s" class="widget %2$s">',
        'after_widget'  => '</section>',
        'before_title'  => '<h2 class="widget-title">',
        'after_title'   => '</h2>',
    ));
    
    register_sidebar(array(
        'name'          => __('Footer 1', 'iptv-pro'),
        'id'            => 'footer-1',
        'description'   => __('Footer widget area 1.', 'iptv-pro'),
        'before_widget' => '<div id="%1$s" class="widget %2$s">',
        'after_widget'  => '</div>',
        'before_title'  => '<h3 class="widget-title">',
        'after_title'   => '</h3>',
    ));
    
    register_sidebar(array(
        'name'          => __('Footer 2', 'iptv-pro'),
        'id'            => 'footer-2',
        'description'   => __('Footer widget area 2.', 'iptv-pro'),
        'before_widget' => '<div id="%1$s" class="widget %2$s">',
        'after_widget'  => '</div>',
        'before_title'  => '<h3 class="widget-title">',
        'after_title'   => '</h3>',
    ));
    
    register_sidebar(array(
        'name'          => __('Footer 3', 'iptv-pro'),
        'id'            => 'footer-3',
        'description'   => __('Footer widget area 3.', 'iptv-pro'),
        'before_widget' => '<div id="%1$s" class="widget %2$s">',
        'after_widget'  => '</div>',
        'before_title'  => '<h3 class="widget-title">',
        'after_title'   => '</h3>',
    ));
}
add_action('widgets_init', 'iptv_pro_widgets_init');

// Custom post types
function iptv_pro_custom_post_types() {
    // IPTV Packages
    register_post_type('iptv_package', array(
        'labels' => array(
            'name' => __('IPTV Packages', 'iptv-pro'),
            'singular_name' => __('IPTV Package', 'iptv-pro'),
            'add_new' => __('Add New Package', 'iptv-pro'),
            'add_new_item' => __('Add New IPTV Package', 'iptv-pro'),
            'edit_item' => __('Edit IPTV Package', 'iptv-pro'),
            'new_item' => __('New IPTV Package', 'iptv-pro'),
            'view_item' => __('View IPTV Package', 'iptv-pro'),
            'search_items' => __('Search IPTV Packages', 'iptv-pro'),
            'not_found' => __('No IPTV packages found', 'iptv-pro'),
            'not_found_in_trash' => __('No IPTV packages found in trash', 'iptv-pro'),
        ),
        'public' => true,
        'has_archive' => true,
        'supports' => array('title', 'editor', 'thumbnail', 'excerpt'),
        'menu_icon' => 'dashicons-video-alt3',
        'rewrite' => array('slug' => 'packages'),
    ));
    
    // Customer Subscriptions
    register_post_type('iptv_subscription', array(
        'labels' => array(
            'name' => __('Subscriptions', 'iptv-pro'),
            'singular_name' => __('Subscription', 'iptv-pro'),
        ),
        'public' => false,
        'show_ui' => true,
        'supports' => array('title'),
        'menu_icon' => 'dashicons-groups',
        'capability_type' => 'post',
        'capabilities' => array(
            'create_posts' => 'manage_options',
        ),
        'map_meta_cap' => true,
    ));
    
    // Support Tickets
    register_post_type('support_ticket', array(
        'labels' => array(
            'name' => __('Support Tickets', 'iptv-pro'),
            'singular_name' => __('Support Ticket', 'iptv-pro'),
        ),
        'public' => false,
        'show_ui' => true,
        'supports' => array('title', 'editor'),
        'menu_icon' => 'dashicons-sos',
        'capability_type' => 'post',
    ));
}
add_action('init', 'iptv_pro_custom_post_types');

// Custom taxonomies
function iptv_pro_custom_taxonomies() {
    // Package Categories
    register_taxonomy('package_category', 'iptv_package', array(
        'labels' => array(
            'name' => __('Package Categories', 'iptv-pro'),
            'singular_name' => __('Package Category', 'iptv-pro'),
        ),
        'hierarchical' => true,
        'public' => true,
        'rewrite' => array('slug' => 'package-category'),
    ));
    
    // Channel Categories
    register_taxonomy('channel_category', 'iptv_package', array(
        'labels' => array(
            'name' => __('Channel Categories', 'iptv-pro'),
            'singular_name' => __('Channel Category', 'iptv-pro'),
        ),
        'hierarchical' => false,
        'public' => true,
        'rewrite' => array('slug' => 'channels'),
    ));
}
add_action('init', 'iptv_pro_custom_taxonomies');

// Add custom meta boxes
function iptv_pro_add_meta_boxes() {
    add_meta_box(
        'iptv_package_details',
        __('Package Details', 'iptv-pro'),
        'iptv_pro_package_details_callback',
        'iptv_package',
        'normal',
        'high'
    );
    
    add_meta_box(
        'iptv_subscription_details',
        __('Subscription Details', 'iptv-pro'),
        'iptv_pro_subscription_details_callback',
        'iptv_subscription',
        'normal',
        'high'
    );
}
add_action('add_meta_boxes', 'iptv_pro_add_meta_boxes');

// Package details meta box callback
function iptv_pro_package_details_callback($post) {
    wp_nonce_field('iptv_package_details_nonce', 'iptv_package_details_nonce');
    
    $price = get_post_meta($post->ID, '_iptv_price', true);
    $duration = get_post_meta($post->ID, '_iptv_duration', true);
    $channels = get_post_meta($post->ID, '_iptv_channels', true);
    $features = get_post_meta($post->ID, '_iptv_features', true);
    $is_featured = get_post_meta($post->ID, '_iptv_featured', true);
    
    echo '<table class="form-table">';
    echo '<tr><th><label for="iptv_price">' . __('Price', 'iptv-pro') . '</label></th>';
    echo '<td><input type="number" step="0.01" id="iptv_price" name="iptv_price" value="' . esc_attr($price) . '" /></td></tr>';
    
    echo '<tr><th><label for="iptv_duration">' . __('Duration (months)', 'iptv-pro') . '</label></th>';
    echo '<td><input type="number" id="iptv_duration" name="iptv_duration" value="' . esc_attr($duration) . '" /></td></tr>';
    
    echo '<tr><th><label for="iptv_channels">' . __('Number of Channels', 'iptv-pro') . '</label></th>';
    echo '<td><input type="number" id="iptv_channels" name="iptv_channels" value="' . esc_attr($channels) . '" /></td></tr>';
    
    echo '<tr><th><label for="iptv_features">' . __('Features (one per line)', 'iptv-pro') . '</label></th>';
    echo '<td><textarea id="iptv_features" name="iptv_features" rows="5" cols="50">' . esc_textarea($features) . '</textarea></td></tr>';
    
    echo '<tr><th><label for="iptv_featured">' . __('Featured Package', 'iptv-pro') . '</label></th>';
    echo '<td><input type="checkbox" id="iptv_featured" name="iptv_featured" value="1" ' . checked($is_featured, 1, false) . ' /></td></tr>';
    echo '</table>';
}

// Subscription details meta box callback
function iptv_pro_subscription_details_callback($post) {
    wp_nonce_field('iptv_subscription_details_nonce', 'iptv_subscription_details_nonce');
    
    $customer_id = get_post_meta($post->ID, '_customer_id', true);
    $package_id = get_post_meta($post->ID, '_package_id', true);
    $start_date = get_post_meta($post->ID, '_start_date', true);
    $end_date = get_post_meta($post->ID, '_end_date', true);
    $status = get_post_meta($post->ID, '_subscription_status', true);
    
    echo '<table class="form-table">';
    echo '<tr><th><label for="customer_id">' . __('Customer ID', 'iptv-pro') . '</label></th>';
    echo '<td><input type="number" id="customer_id" name="customer_id" value="' . esc_attr($customer_id) . '" /></td></tr>';
    
    echo '<tr><th><label for="package_id">' . __('Package ID', 'iptv-pro') . '</label></th>';
    echo '<td><input type="number" id="package_id" name="package_id" value="' . esc_attr($package_id) . '" /></td></tr>';
    
    echo '<tr><th><label for="start_date">' . __('Start Date', 'iptv-pro') . '</label></th>';
    echo '<td><input type="date" id="start_date" name="start_date" value="' . esc_attr($start_date) . '" /></td></tr>';
    
    echo '<tr><th><label for="end_date">' . __('End Date', 'iptv-pro') . '</label></th>';
    echo '<td><input type="date" id="end_date" name="end_date" value="' . esc_attr($end_date) . '" /></td></tr>';
    
    echo '<tr><th><label for="subscription_status">' . __('Status', 'iptv-pro') . '</label></th>';
    echo '<td><select id="subscription_status" name="subscription_status">';
    echo '<option value="active" ' . selected($status, 'active', false) . '>' . __('Active', 'iptv-pro') . '</option>';
    echo '<option value="expired" ' . selected($status, 'expired', false) . '>' . __('Expired', 'iptv-pro') . '</option>';
    echo '<option value="cancelled" ' . selected($status, 'cancelled', false) . '>' . __('Cancelled', 'iptv-pro') . '</option>';
    echo '</select></td></tr>';
    echo '</table>';
}

// Save meta box data
function iptv_pro_save_meta_boxes($post_id) {
    // Package details
    if (isset($_POST['iptv_package_details_nonce']) && wp_verify_nonce($_POST['iptv_package_details_nonce'], 'iptv_package_details_nonce')) {
        if (isset($_POST['iptv_price'])) {
            update_post_meta($post_id, '_iptv_price', sanitize_text_field($_POST['iptv_price']));
        }
        if (isset($_POST['iptv_duration'])) {
            update_post_meta($post_id, '_iptv_duration', sanitize_text_field($_POST['iptv_duration']));
        }
        if (isset($_POST['iptv_channels'])) {
            update_post_meta($post_id, '_iptv_channels', sanitize_text_field($_POST['iptv_channels']));
        }
        if (isset($_POST['iptv_features'])) {
            update_post_meta($post_id, '_iptv_features', sanitize_textarea_field($_POST['iptv_features']));
        }
        update_post_meta($post_id, '_iptv_featured', isset($_POST['iptv_featured']) ? 1 : 0);
    }
    
    // Subscription details
    if (isset($_POST['iptv_subscription_details_nonce']) && wp_verify_nonce($_POST['iptv_subscription_details_nonce'], 'iptv_subscription_details_nonce')) {
        if (isset($_POST['customer_id'])) {
            update_post_meta($post_id, '_customer_id', sanitize_text_field($_POST['customer_id']));
        }
        if (isset($_POST['package_id'])) {
            update_post_meta($post_id, '_package_id', sanitize_text_field($_POST['package_id']));
        }
        if (isset($_POST['start_date'])) {
            update_post_meta($post_id, '_start_date', sanitize_text_field($_POST['start_date']));
        }
        if (isset($_POST['end_date'])) {
            update_post_meta($post_id, '_end_date', sanitize_text_field($_POST['end_date']));
        }
        if (isset($_POST['subscription_status'])) {
            update_post_meta($post_id, '_subscription_status', sanitize_text_field($_POST['subscription_status']));
        }
    }
}
add_action('save_post', 'iptv_pro_save_meta_boxes');

// Include additional functionality files
require_once get_template_directory() . '/inc/customizer.php';
require_once get_template_directory() . '/inc/user-management.php';
require_once get_template_directory() . '/inc/payment-integration.php';
require_once get_template_directory() . '/inc/security.php';
require_once get_template_directory() . '/inc/admin-dashboard.php';

?>
