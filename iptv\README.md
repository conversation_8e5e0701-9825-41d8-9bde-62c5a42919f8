# IPTV Pro WordPress Theme

A comprehensive WordPress theme designed specifically for IPTV service providers and sellers. This theme includes everything needed to run a professional IPTV business online.

## Features

### 🎯 Core Features
- **Customer Management System** - Complete user registration, login, and profile management
- **Subscription Management** - Handle IPTV subscriptions with automatic expiration tracking
- **Payment Integration** - Support for PayPal, Stripe, and cryptocurrency payments
- **Package Management** - Create and manage different IPTV packages with pricing
- **Customer Dashboard** - Full-featured dashboard for customers to manage their accounts
- **Admin Dashboard** - Comprehensive admin panel for business management

### 💳 Payment Features
- PayPal integration for secure payments
- Stripe integration for credit/debit card processing
- Cryptocurrency payment support
- Automatic subscription activation
- Payment history tracking
- Invoice generation

### 🔐 Security Features
- Login attempt limiting and IP blocking
- Two-factor authentication support
- Anti-piracy measures with connection monitoring
- Secure IPTV credential management
- Content protection and watermarking
- Session management and cleanup

### 📱 Customer Features
- User-friendly registration and login
- Customer dashboard with subscription details
- IPTV credentials management
- Payment history and invoicing
- Support ticket system
- App download links
- Setup guides and tutorials

### 🛠️ Admin Features
- Complete customer management
- Subscription monitoring and control
- Payment tracking and reporting
- Revenue analytics and charts
- Bulk customer operations
- Security monitoring and alerts

### 🎨 Design Features
- Modern, responsive design
- Mobile-friendly interface
- Customizable colors and branding
- Professional IPTV-focused layout
- Live chat widget integration
- SEO optimized structure

## Installation

1. **Upload the theme:**
   - Download the theme files
   - Upload to `/wp-content/themes/iptv/`
   - Activate the theme in WordPress admin

2. **Configure settings:**
   - Go to Appearance > Customize
   - Configure contact information, social media, and payment settings
   - Set up your IPTV server details

3. **Create essential pages:**
   - Create pages using the provided page templates:
     - Dashboard (Template: Customer Dashboard)
     - Checkout (Template: Checkout)
     - Support (Template: Support Center)
     - Login, Register, Packages, etc.

4. **Set up payment gateways:**
   - Configure PayPal Client ID in Customizer
   - Add Stripe Publishable Key for card payments
   - Enable cryptocurrency payments if needed

## Required Plugins

While the theme works standalone, these plugins enhance functionality:

- **WooCommerce** (optional) - For advanced e-commerce features
- **Contact Form 7** - For contact forms
- **Yoast SEO** - For SEO optimization
- **UpdraftPlus** - For backups

## Theme Structure

```
iptv/
├── style.css                 # Main stylesheet
├── index.php                 # Main template
├── header.php                # Header template
├── footer.php                # Footer template
├── functions.php             # Theme functions
├── single-iptv_package.php   # Single package template
├── archive-iptv_package.php  # Package archive template
├── assets/
│   ├── js/
│   │   └── main.js          # Main JavaScript
│   ├── css/
│   └── images/
├── inc/
│   ├── customizer.php       # Theme customizer
│   ├── user-management.php  # User functions
│   ├── payment-integration.php # Payment handling
│   ├── security.php         # Security features
│   └── admin-dashboard.php  # Admin panel
├── page-templates/
│   ├── dashboard.php        # Customer dashboard
│   ├── checkout.php         # Checkout page
│   └── support.php          # Support center
└── templates/
```

## Custom Post Types

### IPTV Packages
- **Purpose:** Manage different IPTV subscription packages
- **Fields:** Price, duration, channel count, features, featured status
- **Usage:** Create packages that customers can purchase

### Subscriptions
- **Purpose:** Track customer subscriptions
- **Fields:** Customer ID, package, start/end dates, status
- **Usage:** Monitor active subscriptions and renewals

### Support Tickets
- **Purpose:** Handle customer support requests
- **Fields:** Customer, subject, priority, status, messages
- **Usage:** Provide customer support and track issues

## Customization

### Theme Customizer Options
- **Hero Section:** Customize homepage hero title and subtitle
- **Contact Information:** Set email, phone, WhatsApp number
- **Social Media:** Add social media links
- **Payment Settings:** Configure payment gateways
- **Live Chat:** Enable/disable live chat widget
- **Theme Colors:** Customize primary and secondary colors

### Custom User Fields
- Customer ID (auto-generated)
- Subscription status and end date
- IPTV username and password
- Server URL and max connections
- Package type and billing information

### Security Settings
- Login attempt limiting
- IP blocking for suspicious activity
- Two-factor authentication
- Session monitoring
- Content protection

## Payment Integration

### PayPal Setup
1. Get PayPal Client ID from PayPal Developer Dashboard
2. Add Client ID in Appearance > Customize > Payment Settings
3. Enable PayPal payments

### Stripe Setup
1. Get Stripe Publishable Key from Stripe Dashboard
2. Add key in Appearance > Customize > Payment Settings
3. Enable Stripe payments

### Cryptocurrency
1. Enable crypto payments in customizer
2. Configure wallet addresses for receiving payments
3. Set up payment confirmation process

## Customer Workflow

1. **Registration:** Customer creates account with personal details
2. **Package Selection:** Browse and select IPTV package
3. **Payment:** Complete payment via preferred method
4. **Activation:** Subscription automatically activated
5. **Credentials:** IPTV login details sent via email
6. **Dashboard Access:** Manage subscription via customer dashboard

## Admin Workflow

1. **Package Management:** Create and manage IPTV packages
2. **Customer Monitoring:** Track customer subscriptions and activity
3. **Payment Processing:** Monitor payments and handle issues
4. **Support Management:** Handle customer support tickets
5. **Analytics:** View revenue reports and customer statistics

## Security Best Practices

- Regular WordPress and theme updates
- Strong passwords for admin accounts
- SSL certificate for secure payments
- Regular database backups
- Monitor for suspicious activity
- Implement proper user permissions

## Troubleshooting

### Common Issues

**Payment not processing:**
- Check payment gateway credentials
- Verify SSL certificate is active
- Check error logs for specific issues

**IPTV credentials not generating:**
- Ensure user meta fields are properly saved
- Check email delivery settings
- Verify server URL configuration

**Dashboard not loading:**
- Check user permissions
- Verify page template is correctly assigned
- Clear any caching plugins

### Support

For technical support and customization requests:
- Email: <EMAIL>
- Documentation: Available in theme files
- Updates: Check for theme updates regularly

## License

This theme is licensed under GPL v2 or later. You are free to use, modify, and distribute this theme according to the GPL license terms.

## Credits

- Built with WordPress best practices
- Uses modern CSS Grid and Flexbox
- Responsive design principles
- Security-first approach
- Performance optimized

## Changelog

### Version 1.0.0
- Initial release
- Complete IPTV business management system
- Payment gateway integration
- Customer and admin dashboards
- Security features implementation
- Responsive design
- SEO optimization

---

**Note:** This theme is designed for legitimate IPTV service providers. Ensure you comply with all applicable laws and regulations in your jurisdiction when operating an IPTV service.
