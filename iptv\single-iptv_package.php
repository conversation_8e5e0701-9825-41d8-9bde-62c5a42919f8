<?php
/**
 * Single IPTV Package Template
 *
 * @package IPTV_Pro
 */

get_header(); ?>

<main class="main-content">
    <div class="container" style="padding: 2rem 0;">
        <?php while (have_posts()) : the_post(); 
            $price = get_post_meta(get_the_ID(), '_iptv_price', true);
            $duration = get_post_meta(get_the_ID(), '_iptv_duration', true);
            $channels = get_post_meta(get_the_ID(), '_iptv_channels', true);
            $features = get_post_meta(get_the_ID(), '_iptv_features', true);
            $is_featured = get_post_meta(get_the_ID(), '_iptv_featured', true);
        ?>
            
            <div class="package-header" style="text-align: center; margin-bottom: 3rem;">
                <h1 class="package-title" style="font-size: 3rem; font-weight: 700; margin-bottom: 1rem; color: #1f2937;">
                    <?php the_title(); ?>
                    <?php if ($is_featured) : ?>
                        <span style="background: #3b82f6; color: white; padding: 0.5rem 1rem; border-radius: 20px; font-size: 0.875rem; margin-left: 1rem;">
                            <?php _e('Most Popular', 'iptv-pro'); ?>
                        </span>
                    <?php endif; ?>
                </h1>
                
                <div class="package-price" style="font-size: 4rem; font-weight: 700; color: #3b82f6; margin-bottom: 0.5rem;">
                    $<?php echo esc_html($price); ?>
                </div>
                
                <div class="package-period" style="font-size: 1.25rem; color: #6b7280; margin-bottom: 2rem;">
                    <?php printf(__('for %d months', 'iptv-pro'), $duration); ?>
                </div>
                
                <div class="package-actions" style="margin-bottom: 3rem;">
                    <a href="<?php echo add_query_arg('package', get_the_ID(), get_permalink(get_page_by_path('checkout'))); ?>" class="btn btn-primary" style="font-size: 1.25rem; padding: 1rem 2rem;">
                        <?php _e('Get Started Now', 'iptv-pro'); ?>
                    </a>
                    <a href="<?php echo get_permalink(get_page_by_path('free-trial')); ?>" class="btn btn-secondary" style="font-size: 1.25rem; padding: 1rem 2rem; margin-left: 1rem;">
                        <?php _e('Free Trial', 'iptv-pro'); ?>
                    </a>
                </div>
            </div>

            <div class="package-content" style="display: grid; grid-template-columns: 1fr 1fr; gap: 3rem; margin-bottom: 3rem;">
                
                <!-- Package Details -->
                <div class="package-details" style="background: white; padding: 2rem; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.05);">
                    <h2 style="font-size: 1.5rem; font-weight: 600; margin-bottom: 1.5rem; color: #1f2937;">
                        <?php _e('Package Details', 'iptv-pro'); ?>
                    </h2>
                    
                    <div class="detail-item" style="display: flex; justify-content: space-between; padding: 1rem 0; border-bottom: 1px solid #e5e7eb;">
                        <span style="font-weight: 500; color: #374151;"><?php _e('Channels:', 'iptv-pro'); ?></span>
                        <span style="color: #1f2937; font-weight: 600;"><?php echo esc_html($channels); ?>+</span>
                    </div>
                    
                    <div class="detail-item" style="display: flex; justify-content: space-between; padding: 1rem 0; border-bottom: 1px solid #e5e7eb;">
                        <span style="font-weight: 500; color: #374151;"><?php _e('Duration:', 'iptv-pro'); ?></span>
                        <span style="color: #1f2937; font-weight: 600;"><?php echo esc_html($duration); ?> <?php _e('months', 'iptv-pro'); ?></span>
                    </div>
                    
                    <div class="detail-item" style="display: flex; justify-content: space-between; padding: 1rem 0; border-bottom: 1px solid #e5e7eb;">
                        <span style="font-weight: 500; color: #374151;"><?php _e('Quality:', 'iptv-pro'); ?></span>
                        <span style="color: #1f2937; font-weight: 600;"><?php _e('HD & 4K', 'iptv-pro'); ?></span>
                    </div>
                    
                    <div class="detail-item" style="display: flex; justify-content: space-between; padding: 1rem 0; border-bottom: 1px solid #e5e7eb;">
                        <span style="font-weight: 500; color: #374151;"><?php _e('Devices:', 'iptv-pro'); ?></span>
                        <span style="color: #1f2937; font-weight: 600;"><?php _e('Unlimited', 'iptv-pro'); ?></span>
                    </div>
                    
                    <div class="detail-item" style="display: flex; justify-content: space-between; padding: 1rem 0; border-bottom: 1px solid #e5e7eb;">
                        <span style="font-weight: 500; color: #374151;"><?php _e('Support:', 'iptv-pro'); ?></span>
                        <span style="color: #1f2937; font-weight: 600;"><?php _e('24/7', 'iptv-pro'); ?></span>
                    </div>
                    
                    <div class="detail-item" style="display: flex; justify-content: space-between; padding: 1rem 0;">
                        <span style="font-weight: 500; color: #374151;"><?php _e('VOD Library:', 'iptv-pro'); ?></span>
                        <span style="color: #1f2937; font-weight: 600;"><?php _e('Included', 'iptv-pro'); ?></span>
                    </div>
                </div>

                <!-- Features List -->
                <div class="package-features" style="background: white; padding: 2rem; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.05);">
                    <h2 style="font-size: 1.5rem; font-weight: 600; margin-bottom: 1.5rem; color: #1f2937;">
                        <?php _e('What\'s Included', 'iptv-pro'); ?>
                    </h2>
                    
                    <ul style="list-style: none; padding: 0;">
                        <?php
                        if ($features) {
                            $feature_list = explode("\n", $features);
                            foreach ($feature_list as $feature) {
                                if (trim($feature)) {
                                    echo '<li style="padding: 0.75rem 0; display: flex; align-items: center; border-bottom: 1px solid #f3f4f6;">';
                                    echo '<span style="color: #10b981; font-weight: bold; margin-right: 0.75rem; font-size: 1.25rem;">✓</span>';
                                    echo '<span style="color: #374151;">' . esc_html(trim($feature)) . '</span>';
                                    echo '</li>';
                                }
                            }
                        } else {
                            // Default features
                            $default_features = array(
                                __('Premium HD & 4K Channels', 'iptv-pro'),
                                __('Sports Channels & Events', 'iptv-pro'),
                                __('Movies & TV Series', 'iptv-pro'),
                                __('International Channels', 'iptv-pro'),
                                __('Electronic Program Guide (EPG)', 'iptv-pro'),
                                __('Catch-up TV', 'iptv-pro'),
                                __('Multi-device Support', 'iptv-pro'),
                                __('24/7 Customer Support', 'iptv-pro'),
                                __('No Buffering Guarantee', 'iptv-pro'),
                                __('Regular Updates', 'iptv-pro')
                            );
                            
                            foreach ($default_features as $feature) {
                                echo '<li style="padding: 0.75rem 0; display: flex; align-items: center; border-bottom: 1px solid #f3f4f6;">';
                                echo '<span style="color: #10b981; font-weight: bold; margin-right: 0.75rem; font-size: 1.25rem;">✓</span>';
                                echo '<span style="color: #374151;">' . esc_html($feature) . '</span>';
                                echo '</li>';
                            }
                        }
                        ?>
                    </ul>
                </div>
            </div>

            <!-- Package Description -->
            <?php if (get_the_content()) : ?>
                <div class="package-description" style="background: white; padding: 2rem; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.05); margin-bottom: 3rem;">
                    <h2 style="font-size: 1.5rem; font-weight: 600; margin-bottom: 1.5rem; color: #1f2937;">
                        <?php _e('Package Description', 'iptv-pro'); ?>
                    </h2>
                    <div style="color: #4b5563; line-height: 1.6;">
                        <?php the_content(); ?>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Compatible Devices -->
            <div class="compatible-devices" style="background: white; padding: 2rem; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.05); margin-bottom: 3rem;">
                <h2 style="font-size: 1.5rem; font-weight: 600; margin-bottom: 1.5rem; color: #1f2937; text-align: center;">
                    <?php _e('Compatible Devices', 'iptv-pro'); ?>
                </h2>
                
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 1.5rem; text-align: center;">
                    <div class="device-item">
                        <div style="font-size: 3rem; margin-bottom: 0.5rem;">📱</div>
                        <h4 style="font-weight: 600; color: #1f2937;"><?php _e('Android/iOS', 'iptv-pro'); ?></h4>
                    </div>
                    <div class="device-item">
                        <div style="font-size: 3rem; margin-bottom: 0.5rem;">📺</div>
                        <h4 style="font-weight: 600; color: #1f2937;"><?php _e('Smart TV', 'iptv-pro'); ?></h4>
                    </div>
                    <div class="device-item">
                        <div style="font-size: 3rem; margin-bottom: 0.5rem;">💻</div>
                        <h4 style="font-weight: 600; color: #1f2937;"><?php _e('PC/Mac', 'iptv-pro'); ?></h4>
                    </div>
                    <div class="device-item">
                        <div style="font-size: 3rem; margin-bottom: 0.5rem;">📦</div>
                        <h4 style="font-weight: 600; color: #1f2937;"><?php _e('Android Box', 'iptv-pro'); ?></h4>
                    </div>
                    <div class="device-item">
                        <div style="font-size: 3rem; margin-bottom: 0.5rem;">🎮</div>
                        <h4 style="font-weight: 600; color: #1f2937;"><?php _e('Firestick', 'iptv-pro'); ?></h4>
                    </div>
                    <div class="device-item">
                        <div style="font-size: 3rem; margin-bottom: 0.5rem;">⚡</div>
                        <h4 style="font-weight: 600; color: #1f2937;"><?php _e('MAG Box', 'iptv-pro'); ?></h4>
                    </div>
                </div>
            </div>

            <!-- Call to Action -->
            <div class="package-cta" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 3rem; border-radius: 12px; text-align: center; margin-bottom: 3rem;">
                <h2 style="font-size: 2rem; font-weight: 700; margin-bottom: 1rem;">
                    <?php _e('Ready to Get Started?', 'iptv-pro'); ?>
                </h2>
                <p style="font-size: 1.25rem; margin-bottom: 2rem; opacity: 0.9;">
                    <?php _e('Join thousands of satisfied customers and start enjoying premium IPTV today!', 'iptv-pro'); ?>
                </p>
                <div style="display: flex; gap: 1rem; justify-content: center; flex-wrap: wrap;">
                    <a href="<?php echo add_query_arg('package', get_the_ID(), get_permalink(get_page_by_path('checkout'))); ?>" class="btn btn-primary" style="background: white; color: #3b82f6; font-size: 1.25rem; padding: 1rem 2rem;">
                        <?php _e('Order Now', 'iptv-pro'); ?>
                    </a>
                    <a href="<?php echo get_permalink(get_page_by_path('contact')); ?>" class="btn btn-secondary" style="font-size: 1.25rem; padding: 1rem 2rem;">
                        <?php _e('Contact Us', 'iptv-pro'); ?>
                    </a>
                </div>
            </div>

            <!-- Related Packages -->
            <div class="related-packages">
                <h2 style="font-size: 2rem; font-weight: 700; margin-bottom: 2rem; text-align: center; color: #1f2937;">
                    <?php _e('Other Packages', 'iptv-pro'); ?>
                </h2>
                
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem;">
                    <?php
                    $related_packages = new WP_Query(array(
                        'post_type' => 'iptv_package',
                        'posts_per_page' => 3,
                        'post__not_in' => array(get_the_ID()),
                        'meta_key' => '_iptv_price',
                        'orderby' => 'meta_value_num',
                        'order' => 'ASC'
                    ));
                    
                    if ($related_packages->have_posts()) :
                        while ($related_packages->have_posts()) : $related_packages->the_post();
                            $rel_price = get_post_meta(get_the_ID(), '_iptv_price', true);
                            $rel_duration = get_post_meta(get_the_ID(), '_iptv_duration', true);
                            $rel_channels = get_post_meta(get_the_ID(), '_iptv_channels', true);
                            $rel_is_featured = get_post_meta(get_the_ID(), '_iptv_featured', true);
                            ?>
                            <div class="pricing-card <?php echo $rel_is_featured ? 'featured' : ''; ?>" style="background: white; border-radius: 12px; padding: 2rem; box-shadow: 0 4px 6px rgba(0,0,0,0.05); border: 1px solid #e5e7eb; text-align: center;">
                                <?php if ($rel_is_featured) : ?>
                                    <div class="pricing-badge" style="position: absolute; top: -10px; left: 50%; transform: translateX(-50%); background: #3b82f6; color: white; padding: 0.5rem 1rem; border-radius: 20px; font-size: 0.875rem; font-weight: 600;">
                                        <?php _e('Popular', 'iptv-pro'); ?>
                                    </div>
                                <?php endif; ?>
                                <h3 style="font-size: 1.25rem; font-weight: 700; margin-bottom: 1rem; color: #1f2937;"><?php the_title(); ?></h3>
                                <div style="font-size: 2.5rem; font-weight: 700; color: #3b82f6; margin-bottom: 0.5rem;">$<?php echo esc_html($rel_price); ?></div>
                                <div style="color: #6b7280; font-size: 0.875rem; margin-bottom: 1.5rem;"><?php printf(__('per %d months', 'iptv-pro'), $rel_duration); ?></div>
                                <p style="color: #4b5563; margin-bottom: 1.5rem;"><?php printf(__('%s+ Channels', 'iptv-pro'), $rel_channels); ?></p>
                                <a href="<?php the_permalink(); ?>" class="btn btn-primary" style="width: 100%;">
                                    <?php _e('View Details', 'iptv-pro'); ?>
                                </a>
                            </div>
                            <?php
                        endwhile;
                        wp_reset_postdata();
                    endif;
                    ?>
                </div>
            </div>

        <?php endwhile; ?>
    </div>
</main>

<style>
@media (max-width: 768px) {
    .package-content {
        grid-template-columns: 1fr !important;
    }
    
    .package-header .package-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .package-header .package-actions .btn {
        margin: 0.5rem 0 !important;
        width: 100%;
        max-width: 300px;
    }
    
    .package-cta div {
        flex-direction: column !important;
        align-items: center;
    }
    
    .package-cta .btn {
        margin: 0.5rem 0 !important;
        width: 100%;
        max-width: 300px;
    }
}
</style>

<?php get_footer(); ?>
