<?php
/**
 * Archive template for IPTV Packages
 *
 * @package IPTV_Pro
 */

get_header(); ?>

<main class="main-content">
    <div class="container" style="padding: 2rem 0;">
        
        <!-- Page Header -->
        <div class="page-header" style="text-align: center; margin-bottom: 3rem;">
            <h1 style="font-size: 3rem; font-weight: 700; margin-bottom: 1rem; color: #1f2937;">
                <?php _e('IPTV Packages', 'iptv-pro'); ?>
            </h1>
            <p style="font-size: 1.25rem; color: #6b7280; max-width: 600px; margin: 0 auto;">
                <?php _e('Choose from our premium IPTV packages and enjoy unlimited entertainment with thousands of channels in HD and 4K quality.', 'iptv-pro'); ?>
            </p>
        </div>

        <!-- Package Filters -->
        <div class="package-filters" style="background: white; padding: 1.5rem; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.05); margin-bottom: 3rem;">
            <div style="display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; gap: 1rem;">
                <div class="filter-group">
                    <label for="duration-filter" style="font-weight: 500; margin-right: 0.5rem; color: #374151;">
                        <?php _e('Duration:', 'iptv-pro'); ?>
                    </label>
                    <select id="duration-filter" style="padding: 0.5rem; border: 1px solid #d1d5db; border-radius: 6px;">
                        <option value=""><?php _e('All Durations', 'iptv-pro'); ?></option>
                        <option value="1"><?php _e('1 Month', 'iptv-pro'); ?></option>
                        <option value="3"><?php _e('3 Months', 'iptv-pro'); ?></option>
                        <option value="6"><?php _e('6 Months', 'iptv-pro'); ?></option>
                        <option value="12"><?php _e('12 Months', 'iptv-pro'); ?></option>
                    </select>
                </div>
                
                <div class="filter-group">
                    <label for="price-filter" style="font-weight: 500; margin-right: 0.5rem; color: #374151;">
                        <?php _e('Price Range:', 'iptv-pro'); ?>
                    </label>
                    <select id="price-filter" style="padding: 0.5rem; border: 1px solid #d1d5db; border-radius: 6px;">
                        <option value=""><?php _e('All Prices', 'iptv-pro'); ?></option>
                        <option value="0-20"><?php _e('$0 - $20', 'iptv-pro'); ?></option>
                        <option value="20-50"><?php _e('$20 - $50', 'iptv-pro'); ?></option>
                        <option value="50-100"><?php _e('$50 - $100', 'iptv-pro'); ?></option>
                        <option value="100+"><?php _e('$100+', 'iptv-pro'); ?></option>
                    </select>
                </div>
                
                <div class="filter-group">
                    <label for="sort-filter" style="font-weight: 500; margin-right: 0.5rem; color: #374151;">
                        <?php _e('Sort by:', 'iptv-pro'); ?>
                    </label>
                    <select id="sort-filter" style="padding: 0.5rem; border: 1px solid #d1d5db; border-radius: 6px;">
                        <option value="price-asc"><?php _e('Price: Low to High', 'iptv-pro'); ?></option>
                        <option value="price-desc"><?php _e('Price: High to Low', 'iptv-pro'); ?></option>
                        <option value="duration-asc"><?php _e('Duration: Short to Long', 'iptv-pro'); ?></option>
                        <option value="duration-desc"><?php _e('Duration: Long to Short', 'iptv-pro'); ?></option>
                        <option value="popular"><?php _e('Most Popular', 'iptv-pro'); ?></option>
                    </select>
                </div>
            </div>
        </div>

        <!-- Packages Grid -->
        <?php if (have_posts()) : ?>
            <div class="packages-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 2rem; margin-bottom: 3rem;">
                <?php while (have_posts()) : the_post();
                    $price = get_post_meta(get_the_ID(), '_iptv_price', true);
                    $duration = get_post_meta(get_the_ID(), '_iptv_duration', true);
                    $channels = get_post_meta(get_the_ID(), '_iptv_channels', true);
                    $features = get_post_meta(get_the_ID(), '_iptv_features', true);
                    $is_featured = get_post_meta(get_the_ID(), '_iptv_featured', true);
                ?>
                    <div class="package-card <?php echo $is_featured ? 'featured' : ''; ?>" 
                         data-price="<?php echo esc_attr($price); ?>" 
                         data-duration="<?php echo esc_attr($duration); ?>"
                         style="background: white; border-radius: 12px; padding: 2rem; box-shadow: 0 4px 6px rgba(0,0,0,0.05); border: <?php echo $is_featured ? '2px solid #3b82f6' : '1px solid #e5e7eb'; ?>; position: relative; transition: transform 0.3s, box-shadow 0.3s;">
                        
                        <?php if ($is_featured) : ?>
                            <div style="position: absolute; top: -10px; left: 50%; transform: translateX(-50%); background: #3b82f6; color: white; padding: 0.5rem 1rem; border-radius: 20px; font-size: 0.875rem; font-weight: 600;">
                                <?php _e('Most Popular', 'iptv-pro'); ?>
                            </div>
                        <?php endif; ?>

                        <div class="package-header" style="text-align: center; margin-bottom: 2rem;">
                            <h3 style="font-size: 1.5rem; font-weight: 700; margin-bottom: 1rem; color: #1f2937;">
                                <?php the_title(); ?>
                            </h3>
                            
                            <div style="font-size: 3rem; font-weight: 700; color: #3b82f6; margin-bottom: 0.5rem;">
                                $<?php echo esc_html($price); ?>
                            </div>
                            
                            <div style="color: #6b7280; font-size: 0.875rem; margin-bottom: 1rem;">
                                <?php printf(__('for %d months', 'iptv-pro'), $duration); ?>
                            </div>
                            
                            <div style="background: #f3f4f6; padding: 0.75rem; border-radius: 8px; margin-bottom: 1.5rem;">
                                <strong style="color: #1f2937;"><?php echo esc_html($channels); ?>+ <?php _e('Channels', 'iptv-pro'); ?></strong>
                            </div>
                        </div>

                        <div class="package-features" style="margin-bottom: 2rem;">
                            <ul style="list-style: none; padding: 0;">
                                <?php
                                if ($features) {
                                    $feature_list = array_slice(explode("\n", $features), 0, 5); // Show first 5 features
                                    foreach ($feature_list as $feature) {
                                        if (trim($feature)) {
                                            echo '<li style="padding: 0.5rem 0; display: flex; align-items: center; color: #4b5563;">';
                                            echo '<span style="color: #10b981; font-weight: bold; margin-right: 0.75rem;">✓</span>';
                                            echo esc_html(trim($feature));
                                            echo '</li>';
                                        }
                                    }
                                } else {
                                    // Default features
                                    $default_features = array(
                                        __('HD & 4K Quality', 'iptv-pro'),
                                        __('Sports & Movies', 'iptv-pro'),
                                        __('Multi-device Support', 'iptv-pro'),
                                        __('24/7 Support', 'iptv-pro'),
                                        __('No Buffering', 'iptv-pro')
                                    );
                                    
                                    foreach ($default_features as $feature) {
                                        echo '<li style="padding: 0.5rem 0; display: flex; align-items: center; color: #4b5563;">';
                                        echo '<span style="color: #10b981; font-weight: bold; margin-right: 0.75rem;">✓</span>';
                                        echo esc_html($feature);
                                        echo '</li>';
                                    }
                                }
                                ?>
                            </ul>
                        </div>

                        <div class="package-actions" style="display: flex; flex-direction: column; gap: 0.75rem;">
                            <a href="<?php the_permalink(); ?>" class="btn btn-secondary" style="text-align: center; width: 100%;">
                                <?php _e('View Details', 'iptv-pro'); ?>
                            </a>
                            <a href="<?php echo add_query_arg('package', get_the_ID(), get_permalink(get_page_by_path('checkout'))); ?>" class="btn btn-primary" style="text-align: center; width: 100%;">
                                <?php _e('Order Now', 'iptv-pro'); ?>
                            </a>
                        </div>

                        <?php if (has_excerpt()) : ?>
                            <div class="package-excerpt" style="margin-top: 1rem; padding-top: 1rem; border-top: 1px solid #f3f4f6; color: #6b7280; font-size: 0.875rem;">
                                <?php the_excerpt(); ?>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php endwhile; ?>
            </div>

            <!-- Pagination -->
            <div class="pagination-wrapper" style="text-align: center;">
                <?php
                the_posts_pagination(array(
                    'mid_size' => 2,
                    'prev_text' => __('← Previous', 'iptv-pro'),
                    'next_text' => __('Next →', 'iptv-pro'),
                ));
                ?>
            </div>

        <?php else : ?>
            <div class="no-packages" style="text-align: center; padding: 4rem 0;">
                <h2 style="font-size: 2rem; font-weight: 600; margin-bottom: 1rem; color: #1f2937;">
                    <?php _e('No Packages Found', 'iptv-pro'); ?>
                </h2>
                <p style="color: #6b7280; margin-bottom: 2rem;">
                    <?php _e('We\'re currently updating our packages. Please check back soon or contact us for more information.', 'iptv-pro'); ?>
                </p>
                <a href="<?php echo get_permalink(get_page_by_path('contact')); ?>" class="btn btn-primary">
                    <?php _e('Contact Us', 'iptv-pro'); ?>
                </a>
            </div>
        <?php endif; ?>

        <!-- Call to Action Section -->
        <div class="packages-cta" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 3rem; border-radius: 12px; text-align: center; margin-top: 3rem;">
            <h2 style="font-size: 2rem; font-weight: 700; margin-bottom: 1rem;">
                <?php _e('Need Help Choosing?', 'iptv-pro'); ?>
            </h2>
            <p style="font-size: 1.25rem; margin-bottom: 2rem; opacity: 0.9;">
                <?php _e('Our experts are here to help you find the perfect IPTV package for your needs.', 'iptv-pro'); ?>
            </p>
            <div style="display: flex; gap: 1rem; justify-content: center; flex-wrap: wrap;">
                <a href="<?php echo get_permalink(get_page_by_path('contact')); ?>" class="btn btn-primary" style="background: white; color: #3b82f6;">
                    <?php _e('Contact Us', 'iptv-pro'); ?>
                </a>
                <a href="<?php echo get_permalink(get_page_by_path('free-trial')); ?>" class="btn btn-secondary">
                    <?php _e('Try Free Trial', 'iptv-pro'); ?>
                </a>
            </div>
        </div>
    </div>
</main>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const durationFilter = document.getElementById('duration-filter');
    const priceFilter = document.getElementById('price-filter');
    const sortFilter = document.getElementById('sort-filter');
    const packageCards = document.querySelectorAll('.package-card');

    function filterPackages() {
        const duration = durationFilter.value;
        const priceRange = priceFilter.value;
        
        packageCards.forEach(card => {
            let show = true;
            
            // Duration filter
            if (duration && card.dataset.duration !== duration) {
                show = false;
            }
            
            // Price filter
            if (priceRange && show) {
                const price = parseFloat(card.dataset.price);
                if (priceRange === '0-20' && (price < 0 || price > 20)) show = false;
                if (priceRange === '20-50' && (price < 20 || price > 50)) show = false;
                if (priceRange === '50-100' && (price < 50 || price > 100)) show = false;
                if (priceRange === '100+' && price < 100) show = false;
            }
            
            card.style.display = show ? 'block' : 'none';
        });
    }

    function sortPackages() {
        const sortBy = sortFilter.value;
        const grid = document.querySelector('.packages-grid');
        const cards = Array.from(packageCards);
        
        cards.sort((a, b) => {
            const priceA = parseFloat(a.dataset.price);
            const priceB = parseFloat(b.dataset.price);
            const durationA = parseInt(a.dataset.duration);
            const durationB = parseInt(b.dataset.duration);
            
            switch (sortBy) {
                case 'price-asc':
                    return priceA - priceB;
                case 'price-desc':
                    return priceB - priceA;
                case 'duration-asc':
                    return durationA - durationB;
                case 'duration-desc':
                    return durationB - durationA;
                case 'popular':
                    return (b.classList.contains('featured') ? 1 : 0) - (a.classList.contains('featured') ? 1 : 0);
                default:
                    return 0;
            }
        });
        
        cards.forEach(card => grid.appendChild(card));
    }

    // Add hover effects
    packageCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
            this.style.boxShadow = '0 10px 25px rgba(0,0,0,0.1)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = '0 4px 6px rgba(0,0,0,0.05)';
        });
    });

    // Event listeners
    durationFilter.addEventListener('change', filterPackages);
    priceFilter.addEventListener('change', filterPackages);
    sortFilter.addEventListener('change', sortPackages);
});
</script>

<style>
@media (max-width: 768px) {
    .package-filters > div {
        flex-direction: column !important;
        align-items: stretch !important;
    }
    
    .filter-group {
        margin-bottom: 1rem;
    }
    
    .filter-group select {
        width: 100%;
    }
    
    .packages-grid {
        grid-template-columns: 1fr !important;
    }
    
    .packages-cta div {
        flex-direction: column !important;
        align-items: center;
    }
    
    .packages-cta .btn {
        margin: 0.5rem 0 !important;
        width: 100%;
        max-width: 300px;
    }
}
</style>

<?php get_footer(); ?>
