<?php
/**
 * Theme Customizer
 *
 * @package IPTV_Pro
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Add postMessage support for site title and description for the Theme Customizer.
 */
function iptv_pro_customize_register($wp_customize) {
    
    // Hero Section
    $wp_customize->add_section('iptv_hero_section', array(
        'title'    => __('Hero Section', 'iptv-pro'),
        'priority' => 30,
    ));

    $wp_customize->add_setting('hero_title', array(
        'default'           => __('Premium IPTV Services', 'iptv-pro'),
        'sanitize_callback' => 'sanitize_text_field',
    ));

    $wp_customize->add_control('hero_title', array(
        'label'   => __('Hero Title', 'iptv-pro'),
        'section' => 'iptv_hero_section',
        'type'    => 'text',
    ));

    $wp_customize->add_setting('hero_subtitle', array(
        'default'           => __('Experience unlimited entertainment with our high-quality IPTV packages. Stream thousands of channels in HD and 4K quality.', 'iptv-pro'),
        'sanitize_callback' => 'sanitize_textarea_field',
    ));

    $wp_customize->add_control('hero_subtitle', array(
        'label'   => __('Hero Subtitle', 'iptv-pro'),
        'section' => 'iptv_hero_section',
        'type'    => 'textarea',
    ));

    // Contact Information
    $wp_customize->add_section('iptv_contact_info', array(
        'title'    => __('Contact Information', 'iptv-pro'),
        'priority' => 40,
    ));

    $wp_customize->add_setting('contact_email', array(
        'default'           => '',
        'sanitize_callback' => 'sanitize_email',
    ));

    $wp_customize->add_control('contact_email', array(
        'label'   => __('Contact Email', 'iptv-pro'),
        'section' => 'iptv_contact_info',
        'type'    => 'email',
    ));

    $wp_customize->add_setting('contact_phone', array(
        'default'           => '',
        'sanitize_callback' => 'sanitize_text_field',
    ));

    $wp_customize->add_control('contact_phone', array(
        'label'   => __('Contact Phone', 'iptv-pro'),
        'section' => 'iptv_contact_info',
        'type'    => 'text',
    ));

    $wp_customize->add_setting('whatsapp_number', array(
        'default'           => '',
        'sanitize_callback' => 'sanitize_text_field',
    ));

    $wp_customize->add_control('whatsapp_number', array(
        'label'       => __('WhatsApp Number', 'iptv-pro'),
        'description' => __('Include country code (e.g., +1234567890)', 'iptv-pro'),
        'section'     => 'iptv_contact_info',
        'type'        => 'text',
    ));

    $wp_customize->add_setting('support_hours', array(
        'default'           => __('24/7 Available', 'iptv-pro'),
        'sanitize_callback' => 'sanitize_text_field',
    ));

    $wp_customize->add_control('support_hours', array(
        'label'   => __('Support Hours', 'iptv-pro'),
        'section' => 'iptv_contact_info',
        'type'    => 'text',
    ));

    // Social Media
    $wp_customize->add_section('iptv_social_media', array(
        'title'    => __('Social Media', 'iptv-pro'),
        'priority' => 50,
    ));

    $wp_customize->add_setting('facebook_url', array(
        'default'           => '',
        'sanitize_callback' => 'esc_url_raw',
    ));

    $wp_customize->add_control('facebook_url', array(
        'label'   => __('Facebook URL', 'iptv-pro'),
        'section' => 'iptv_social_media',
        'type'    => 'url',
    ));

    $wp_customize->add_setting('twitter_url', array(
        'default'           => '',
        'sanitize_callback' => 'esc_url_raw',
    ));

    $wp_customize->add_control('twitter_url', array(
        'label'   => __('Twitter URL', 'iptv-pro'),
        'section' => 'iptv_social_media',
        'type'    => 'url',
    ));

    $wp_customize->add_setting('instagram_url', array(
        'default'           => '',
        'sanitize_callback' => 'esc_url_raw',
    ));

    $wp_customize->add_control('instagram_url', array(
        'label'   => __('Instagram URL', 'iptv-pro'),
        'section' => 'iptv_social_media',
        'type'    => 'url',
    ));

    $wp_customize->add_setting('telegram_url', array(
        'default'           => '',
        'sanitize_callback' => 'esc_url_raw',
    ));

    $wp_customize->add_control('telegram_url', array(
        'label'   => __('Telegram URL', 'iptv-pro'),
        'section' => 'iptv_social_media',
        'type'    => 'url',
    ));

    // Footer Settings
    $wp_customize->add_section('iptv_footer_settings', array(
        'title'    => __('Footer Settings', 'iptv-pro'),
        'priority' => 60,
    ));

    $wp_customize->add_setting('footer_about', array(
        'default'           => __('We provide premium IPTV services with thousands of channels, reliable streaming, and excellent customer support. Your entertainment is our priority.', 'iptv-pro'),
        'sanitize_callback' => 'sanitize_textarea_field',
    ));

    $wp_customize->add_control('footer_about', array(
        'label'   => __('Footer About Text', 'iptv-pro'),
        'section' => 'iptv_footer_settings',
        'type'    => 'textarea',
    ));

    // Live Chat Settings
    $wp_customize->add_section('iptv_live_chat', array(
        'title'    => __('Live Chat Settings', 'iptv-pro'),
        'priority' => 70,
    ));

    $wp_customize->add_setting('enable_live_chat', array(
        'default'           => true,
        'sanitize_callback' => 'iptv_pro_sanitize_checkbox',
    ));

    $wp_customize->add_control('enable_live_chat', array(
        'label'   => __('Enable Live Chat Widget', 'iptv-pro'),
        'section' => 'iptv_live_chat',
        'type'    => 'checkbox',
    ));

    // Payment Settings
    $wp_customize->add_section('iptv_payment_settings', array(
        'title'    => __('Payment Settings', 'iptv-pro'),
        'priority' => 80,
    ));

    $wp_customize->add_setting('enable_paypal', array(
        'default'           => true,
        'sanitize_callback' => 'iptv_pro_sanitize_checkbox',
    ));

    $wp_customize->add_control('enable_paypal', array(
        'label'   => __('Enable PayPal', 'iptv-pro'),
        'section' => 'iptv_payment_settings',
        'type'    => 'checkbox',
    ));

    $wp_customize->add_setting('paypal_client_id', array(
        'default'           => '',
        'sanitize_callback' => 'sanitize_text_field',
    ));

    $wp_customize->add_control('paypal_client_id', array(
        'label'   => __('PayPal Client ID', 'iptv-pro'),
        'section' => 'iptv_payment_settings',
        'type'    => 'text',
    ));

    $wp_customize->add_setting('enable_stripe', array(
        'default'           => true,
        'sanitize_callback' => 'iptv_pro_sanitize_checkbox',
    ));

    $wp_customize->add_control('enable_stripe', array(
        'label'   => __('Enable Stripe', 'iptv-pro'),
        'section' => 'iptv_payment_settings',
        'type'    => 'checkbox',
    ));

    $wp_customize->add_setting('stripe_publishable_key', array(
        'default'           => '',
        'sanitize_callback' => 'sanitize_text_field',
    ));

    $wp_customize->add_control('stripe_publishable_key', array(
        'label'   => __('Stripe Publishable Key', 'iptv-pro'),
        'section' => 'iptv_payment_settings',
        'type'    => 'text',
    ));

    $wp_customize->add_setting('enable_crypto', array(
        'default'           => false,
        'sanitize_callback' => 'iptv_pro_sanitize_checkbox',
    ));

    $wp_customize->add_control('enable_crypto', array(
        'label'   => __('Enable Cryptocurrency Payments', 'iptv-pro'),
        'section' => 'iptv_payment_settings',
        'type'    => 'checkbox',
    ));

    // Theme Colors
    $wp_customize->add_section('iptv_theme_colors', array(
        'title'    => __('Theme Colors', 'iptv-pro'),
        'priority' => 90,
    ));

    $wp_customize->add_setting('primary_color', array(
        'default'           => '#3b82f6',
        'sanitize_callback' => 'sanitize_hex_color',
    ));

    $wp_customize->add_control(new WP_Customize_Color_Control($wp_customize, 'primary_color', array(
        'label'   => __('Primary Color', 'iptv-pro'),
        'section' => 'iptv_theme_colors',
    )));

    $wp_customize->add_setting('secondary_color', array(
        'default'           => '#667eea',
        'sanitize_callback' => 'sanitize_hex_color',
    ));

    $wp_customize->add_control(new WP_Customize_Color_Control($wp_customize, 'secondary_color', array(
        'label'   => __('Secondary Color', 'iptv-pro'),
        'section' => 'iptv_theme_colors',
    )));
}
add_action('customize_register', 'iptv_pro_customize_register');

/**
 * Sanitize checkbox
 */
function iptv_pro_sanitize_checkbox($checked) {
    return ((isset($checked) && true == $checked) ? true : false);
}

/**
 * Binds JS handlers to make Theme Customizer preview reload changes asynchronously.
 */
function iptv_pro_customize_preview_js() {
    wp_enqueue_script('iptv-pro-customizer', get_template_directory_uri() . '/assets/js/customizer.js', array('customize-preview'), '1.0.0', true);
}
add_action('customize_preview_init', 'iptv_pro_customize_preview_js');

/**
 * Output custom CSS based on customizer settings
 */
function iptv_pro_customizer_css() {
    $primary_color = get_theme_mod('primary_color', '#3b82f6');
    $secondary_color = get_theme_mod('secondary_color', '#667eea');
    
    ?>
    <style type="text/css">
        :root {
            --primary-color: <?php echo esc_attr($primary_color); ?>;
            --secondary-color: <?php echo esc_attr($secondary_color); ?>;
        }
        
        .btn-primary,
        .pricing-card.featured {
            background: var(--primary-color) !important;
            border-color: var(--primary-color) !important;
        }
        
        .site-header,
        .hero-section {
            background: linear-gradient(135deg, var(--secondary-color) 0%, var(--primary-color) 100%) !important;
        }
        
        .feature-icon {
            background: linear-gradient(135deg, var(--secondary-color) 0%, var(--primary-color) 100%) !important;
        }
        
        .pricing-price {
            color: var(--primary-color) !important;
        }
        
        a {
            color: var(--primary-color);
        }
        
        a:hover {
            color: var(--secondary-color);
        }
    </style>
    <?php
}
add_action('wp_head', 'iptv_pro_customizer_css');

?>
