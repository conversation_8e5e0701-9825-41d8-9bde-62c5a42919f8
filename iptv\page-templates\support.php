<?php
/**
 * Template Name: Support Center
 *
 * @package IPTV_Pro
 */

get_header(); ?>

<main class="main-content support-page">
    <div class="container" style="padding: 2rem 0;">
        
        <!-- Support Header -->
        <div class="support-header" style="text-align: center; margin-bottom: 3rem;">
            <h1 style="font-size: 3rem; font-weight: 700; margin-bottom: 1rem; color: #1f2937;">
                <?php _e('Support Center', 'iptv-pro'); ?>
            </h1>
            <p style="font-size: 1.25rem; color: #6b7280; max-width: 600px; margin: 0 auto;">
                <?php _e('Get help with your IPTV service. Our support team is here to assist you 24/7.', 'iptv-pro'); ?>
            </p>
        </div>

        <!-- Support Options -->
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem; margin-bottom: 3rem;">
            
            <!-- Live Chat -->
            <div style="background: white; padding: 2rem; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.05); text-align: center;">
                <div style="font-size: 3rem; margin-bottom: 1rem;">💬</div>
                <h3 style="font-size: 1.25rem; font-weight: 600; margin-bottom: 1rem; color: #1f2937;">
                    <?php _e('Live Chat', 'iptv-pro'); ?>
                </h3>
                <p style="color: #6b7280; margin-bottom: 1.5rem;">
                    <?php _e('Get instant help from our support team. Available 24/7 for urgent issues.', 'iptv-pro'); ?>
                </p>
                <button id="start-live-chat" class="btn btn-primary" style="width: 100%;">
                    <?php _e('Start Chat', 'iptv-pro'); ?>
                </button>
            </div>

            <!-- WhatsApp Support -->
            <?php if (get_theme_mod('whatsapp_number')) : ?>
                <div style="background: white; padding: 2rem; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.05); text-align: center;">
                    <div style="font-size: 3rem; margin-bottom: 1rem;">📱</div>
                    <h3 style="font-size: 1.25rem; font-weight: 600; margin-bottom: 1rem; color: #1f2937;">
                        <?php _e('WhatsApp Support', 'iptv-pro'); ?>
                    </h3>
                    <p style="color: #6b7280; margin-bottom: 1.5rem;">
                        <?php _e('Contact us directly on WhatsApp for quick assistance and support.', 'iptv-pro'); ?>
                    </p>
                    <a href="https://wa.me/<?php echo esc_attr(str_replace('+', '', get_theme_mod('whatsapp_number'))); ?>" target="_blank" class="btn btn-primary" style="width: 100%; background: #25d366;">
                        <?php _e('Open WhatsApp', 'iptv-pro'); ?>
                    </a>
                </div>
            <?php endif; ?>

            <!-- Email Support -->
            <div style="background: white; padding: 2rem; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.05); text-align: center;">
                <div style="font-size: 3rem; margin-bottom: 1rem;">📧</div>
                <h3 style="font-size: 1.25rem; font-weight: 600; margin-bottom: 1rem; color: #1f2937;">
                    <?php _e('Email Support', 'iptv-pro'); ?>
                </h3>
                <p style="color: #6b7280; margin-bottom: 1.5rem;">
                    <?php _e('Send us an email and we\'ll get back to you within 24 hours.', 'iptv-pro'); ?>
                </p>
                <a href="mailto:<?php echo esc_attr(get_theme_mod('contact_email', get_option('admin_email'))); ?>" class="btn btn-primary" style="width: 100%;">
                    <?php _e('Send Email', 'iptv-pro'); ?>
                </a>
            </div>

            <!-- Submit Ticket -->
            <div style="background: white; padding: 2rem; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.05); text-align: center;">
                <div style="font-size: 3rem; margin-bottom: 1rem;">🎫</div>
                <h3 style="font-size: 1.25rem; font-weight: 600; margin-bottom: 1rem; color: #1f2937;">
                    <?php _e('Support Ticket', 'iptv-pro'); ?>
                </h3>
                <p style="color: #6b7280; margin-bottom: 1.5rem;">
                    <?php _e('Submit a detailed support ticket for complex issues or account problems.', 'iptv-pro'); ?>
                </p>
                <a href="#ticket-form" class="btn btn-primary" style="width: 100%;">
                    <?php _e('Submit Ticket', 'iptv-pro'); ?>
                </a>
            </div>
        </div>

        <!-- FAQ Section -->
        <div style="background: white; padding: 2rem; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.05); margin-bottom: 3rem;">
            <h2 style="font-size: 2rem; font-weight: 700; margin-bottom: 2rem; text-align: center; color: #1f2937;">
                <?php _e('Frequently Asked Questions', 'iptv-pro'); ?>
            </h2>
            
            <div class="faq-container">
                <div class="faq-item" style="border-bottom: 1px solid #e5e7eb; padding: 1.5rem 0;">
                    <button class="faq-question" style="width: 100%; text-align: left; background: none; border: none; font-size: 1.125rem; font-weight: 600; color: #1f2937; cursor: pointer; display: flex; justify-content: space-between; align-items: center;">
                        <?php _e('How do I set up my IPTV service?', 'iptv-pro'); ?>
                        <span class="faq-toggle">+</span>
                    </button>
                    <div class="faq-answer" style="display: none; padding-top: 1rem; color: #6b7280; line-height: 1.6;">
                        <p><?php _e('Setting up your IPTV service is easy! After purchasing a subscription, you\'ll receive your login credentials via email. Simply download our recommended app, enter your credentials, and start watching. We also provide detailed setup guides for all devices.', 'iptv-pro'); ?></p>
                    </div>
                </div>

                <div class="faq-item" style="border-bottom: 1px solid #e5e7eb; padding: 1.5rem 0;">
                    <button class="faq-question" style="width: 100%; text-align: left; background: none; border: none; font-size: 1.125rem; font-weight: 600; color: #1f2937; cursor: pointer; display: flex; justify-content: space-between; align-items: center;">
                        <?php _e('What devices are supported?', 'iptv-pro'); ?>
                        <span class="faq-toggle">+</span>
                    </button>
                    <div class="faq-answer" style="display: none; padding-top: 1rem; color: #6b7280; line-height: 1.6;">
                        <p><?php _e('Our IPTV service works on virtually all devices including Smart TVs, Android/iOS phones and tablets, Windows/Mac computers, Android TV boxes, Amazon Fire Stick, MAG boxes, and more.', 'iptv-pro'); ?></p>
                    </div>
                </div>

                <div class="faq-item" style="border-bottom: 1px solid #e5e7eb; padding: 1.5rem 0;">
                    <button class="faq-question" style="width: 100%; text-align: left; background: none; border: none; font-size: 1.125rem; font-weight: 600; color: #1f2937; cursor: pointer; display: flex; justify-content: space-between; align-items: center;">
                        <?php _e('How many devices can I use simultaneously?', 'iptv-pro'); ?>
                        <span class="faq-toggle">+</span>
                    </button>
                    <div class="faq-answer" style="display: none; padding-top: 1rem; color: #6b7280; line-height: 1.6;">
                        <p><?php _e('The number of simultaneous connections depends on your subscription package. Most packages include 1-2 connections, but we offer packages with up to 5 simultaneous connections for larger households.', 'iptv-pro'); ?></p>
                    </div>
                </div>

                <div class="faq-item" style="border-bottom: 1px solid #e5e7eb; padding: 1.5rem 0;">
                    <button class="faq-question" style="width: 100%; text-align: left; background: none; border: none; font-size: 1.125rem; font-weight: 600; color: #1f2937; cursor: pointer; display: flex; justify-content: space-between; align-items: center;">
                        <?php _e('What should I do if channels are buffering?', 'iptv-pro'); ?>
                        <span class="faq-toggle">+</span>
                    </button>
                    <div class="faq-answer" style="display: none; padding-top: 1rem; color: #6b7280; line-height: 1.6;">
                        <p><?php _e('Buffering is usually caused by internet connection issues. Ensure you have a stable internet connection with at least 10 Mbps for HD content. Try restarting your router, closing other apps using internet, or switching to a different server if available.', 'iptv-pro'); ?></p>
                    </div>
                </div>

                <div class="faq-item" style="border-bottom: 1px solid #e5e7eb; padding: 1.5rem 0;">
                    <button class="faq-question" style="width: 100%; text-align: left; background: none; border: none; font-size: 1.125rem; font-weight: 600; color: #1f2937; cursor: pointer; display: flex; justify-content: space-between; align-items: center;">
                        <?php _e('Can I cancel my subscription anytime?', 'iptv-pro'); ?>
                        <span class="faq-toggle">+</span>
                    </button>
                    <div class="faq-answer" style="display: none; padding-top: 1rem; color: #6b7280; line-height: 1.6;">
                        <p><?php _e('Yes, you can cancel your subscription at any time from your customer dashboard. Please note that we don\'t offer refunds for partial months, but you can continue using the service until your current billing period ends.', 'iptv-pro'); ?></p>
                    </div>
                </div>

                <div class="faq-item" style="padding: 1.5rem 0;">
                    <button class="faq-question" style="width: 100%; text-align: left; background: none; border: none; font-size: 1.125rem; font-weight: 600; color: #1f2937; cursor: pointer; display: flex; justify-content: space-between; align-items: center;">
                        <?php _e('Do you offer a free trial?', 'iptv-pro'); ?>
                        <span class="faq-toggle">+</span>
                    </button>
                    <div class="faq-answer" style="display: none; padding-top: 1rem; color: #6b7280; line-height: 1.6;">
                        <p><?php _e('Yes! We offer a 24-hour free trial so you can test our service quality and channel selection before making a purchase. The trial includes access to all channels and features.', 'iptv-pro'); ?></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Support Ticket Form -->
        <div id="ticket-form" style="background: white; padding: 2rem; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.05); margin-bottom: 3rem;">
            <h2 style="font-size: 2rem; font-weight: 700; margin-bottom: 2rem; text-align: center; color: #1f2937;">
                <?php _e('Submit Support Ticket', 'iptv-pro'); ?>
            </h2>
            
            <form class="support-form" style="max-width: 600px; margin: 0 auto;">
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; margin-bottom: 1rem;">
                    <div>
                        <label style="display: block; font-weight: 500; margin-bottom: 0.5rem; color: #374151;">
                            <?php _e('Your Name', 'iptv-pro'); ?> <span style="color: #ef4444;">*</span>
                        </label>
                        <input type="text" name="name" required style="width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 6px;">
                    </div>
                    
                    <div>
                        <label style="display: block; font-weight: 500; margin-bottom: 0.5rem; color: #374151;">
                            <?php _e('Email Address', 'iptv-pro'); ?> <span style="color: #ef4444;">*</span>
                        </label>
                        <input type="email" name="email" required style="width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 6px;">
                    </div>
                </div>
                
                <div style="margin-bottom: 1rem;">
                    <label style="display: block; font-weight: 500; margin-bottom: 0.5rem; color: #374151;">
                        <?php _e('Subject', 'iptv-pro'); ?> <span style="color: #ef4444;">*</span>
                    </label>
                    <input type="text" name="subject" required style="width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 6px;">
                </div>
                
                <div style="margin-bottom: 1rem;">
                    <label style="display: block; font-weight: 500; margin-bottom: 0.5rem; color: #374151;">
                        <?php _e('Priority', 'iptv-pro'); ?>
                    </label>
                    <select name="priority" style="width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 6px;">
                        <option value="low"><?php _e('Low', 'iptv-pro'); ?></option>
                        <option value="medium" selected><?php _e('Medium', 'iptv-pro'); ?></option>
                        <option value="high"><?php _e('High', 'iptv-pro'); ?></option>
                        <option value="urgent"><?php _e('Urgent', 'iptv-pro'); ?></option>
                    </select>
                </div>
                
                <div style="margin-bottom: 1rem;">
                    <label style="display: block; font-weight: 500; margin-bottom: 0.5rem; color: #374151;">
                        <?php _e('Category', 'iptv-pro'); ?>
                    </label>
                    <select name="category" style="width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 6px;">
                        <option value="technical"><?php _e('Technical Issue', 'iptv-pro'); ?></option>
                        <option value="billing"><?php _e('Billing Question', 'iptv-pro'); ?></option>
                        <option value="account"><?php _e('Account Issue', 'iptv-pro'); ?></option>
                        <option value="channels"><?php _e('Channel Problem', 'iptv-pro'); ?></option>
                        <option value="setup"><?php _e('Setup Help', 'iptv-pro'); ?></option>
                        <option value="other"><?php _e('Other', 'iptv-pro'); ?></option>
                    </select>
                </div>
                
                <div style="margin-bottom: 1rem;">
                    <label style="display: block; font-weight: 500; margin-bottom: 0.5rem; color: #374151;">
                        <?php _e('Message', 'iptv-pro'); ?> <span style="color: #ef4444;">*</span>
                    </label>
                    <textarea name="message" rows="6" required style="width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 6px; resize: vertical;" placeholder="<?php _e('Please describe your issue in detail...', 'iptv-pro'); ?>"></textarea>
                </div>
                
                <div style="margin-bottom: 2rem;">
                    <label style="display: block; font-weight: 500; margin-bottom: 0.5rem; color: #374151;">
                        <?php _e('Attachment (optional)', 'iptv-pro'); ?>
                    </label>
                    <input type="file" name="attachment" accept=".jpg,.jpeg,.png,.gif,.pdf,.txt" style="width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 6px;">
                    <small style="color: #6b7280;"><?php _e('Max file size: 5MB. Allowed formats: JPG, PNG, GIF, PDF, TXT', 'iptv-pro'); ?></small>
                </div>
                
                <button type="submit" class="btn btn-primary submit-btn" style="width: 100%; font-size: 1.125rem; padding: 1rem;">
                    <?php _e('Submit Ticket', 'iptv-pro'); ?>
                </button>
            </form>
        </div>

        <!-- Contact Information -->
        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 3rem; border-radius: 12px; text-align: center;">
            <h2 style="font-size: 2rem; font-weight: 700; margin-bottom: 2rem;">
                <?php _e('Need Immediate Help?', 'iptv-pro'); ?>
            </h2>
            
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 2rem; margin-bottom: 2rem;">
                <?php if (get_theme_mod('contact_phone')) : ?>
                    <div>
                        <div style="font-size: 2rem; margin-bottom: 0.5rem;">📞</div>
                        <h4 style="font-weight: 600; margin-bottom: 0.5rem;"><?php _e('Call Us', 'iptv-pro'); ?></h4>
                        <a href="tel:<?php echo esc_attr(get_theme_mod('contact_phone')); ?>" style="color: white; text-decoration: none;">
                            <?php echo esc_html(get_theme_mod('contact_phone')); ?>
                        </a>
                    </div>
                <?php endif; ?>
                
                <div>
                    <div style="font-size: 2rem; margin-bottom: 0.5rem;">📧</div>
                    <h4 style="font-weight: 600; margin-bottom: 0.5rem;"><?php _e('Email Us', 'iptv-pro'); ?></h4>
                    <a href="mailto:<?php echo esc_attr(get_theme_mod('contact_email', get_option('admin_email'))); ?>" style="color: white; text-decoration: none;">
                        <?php echo esc_html(get_theme_mod('contact_email', get_option('admin_email'))); ?>
                    </a>
                </div>
                
                <div>
                    <div style="font-size: 2rem; margin-bottom: 0.5rem;">⏰</div>
                    <h4 style="font-weight: 600; margin-bottom: 0.5rem;"><?php _e('Support Hours', 'iptv-pro'); ?></h4>
                    <span><?php echo esc_html(get_theme_mod('support_hours', __('24/7 Available', 'iptv-pro'))); ?></span>
                </div>
            </div>
            
            <p style="opacity: 0.9; font-size: 1.125rem;">
                <?php _e('Our dedicated support team is here to help you get the most out of your IPTV service.', 'iptv-pro'); ?>
            </p>
        </div>
    </div>
</main>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // FAQ Toggle
    const faqQuestions = document.querySelectorAll('.faq-question');
    
    faqQuestions.forEach(question => {
        question.addEventListener('click', function() {
            const answer = this.nextElementSibling;
            const toggle = this.querySelector('.faq-toggle');
            
            if (answer.style.display === 'none' || answer.style.display === '') {
                answer.style.display = 'block';
                toggle.textContent = '−';
            } else {
                answer.style.display = 'none';
                toggle.textContent = '+';
            }
        });
    });
    
    // Live Chat
    document.getElementById('start-live-chat').addEventListener('click', function() {
        // Trigger the live chat widget
        const chatWidget = document.getElementById('live-chat-widget');
        if (chatWidget) {
            const chatToggle = chatWidget.querySelector('#chat-toggle');
            if (chatToggle) {
                chatToggle.click();
            }
        }
    });
    
    // Smooth scroll to ticket form
    document.querySelectorAll('a[href="#ticket-form"]').forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            document.getElementById('ticket-form').scrollIntoView({
                behavior: 'smooth'
            });
        });
    });
});
</script>

<style>
@media (max-width: 768px) {
    .support-form > div:first-child {
        grid-template-columns: 1fr !important;
    }
    
    .faq-question {
        font-size: 1rem !important;
    }
    
    .support-header h1 {
        font-size: 2rem !important;
    }
}
</style>

<?php get_footer(); ?>
