/**
 * Main JavaScript file for IPTV Pro Theme
 */

(function($) {
    'use strict';

    // Document ready
    $(document).ready(function() {
        
        // Initialize all components
        initMobileMenu();
        initSmoothScrolling();
        initPricingCards();
        initContactForms();
        initCustomerDashboard();
        initPaymentForms();
        initLiveChat();
        initBackToTop();
        
    });

    // Mobile menu functionality
    function initMobileMenu() {
        $('.mobile-menu-toggle').on('click', function() {
            $('.mobile-nav').slideToggle();
            $(this).toggleClass('active');
        });

        // Close mobile menu when clicking outside
        $(document).on('click', function(e) {
            if (!$(e.target).closest('.site-header').length) {
                $('.mobile-nav').slideUp();
                $('.mobile-menu-toggle').removeClass('active');
            }
        });

        // Close mobile menu on window resize
        $(window).on('resize', function() {
            if ($(window).width() > 768) {
                $('.mobile-nav').hide();
                $('.mobile-menu-toggle').removeClass('active');
            }
        });
    }

    // Smooth scrolling for anchor links
    function initSmoothScrolling() {
        $('a[href^="#"]').on('click', function(e) {
            e.preventDefault();
            
            var target = $(this.getAttribute('href'));
            if (target.length) {
                $('html, body').animate({
                    scrollTop: target.offset().top - 80
                }, 800);
            }
        });
    }

    // Pricing cards interactions
    function initPricingCards() {
        $('.pricing-card').hover(
            function() {
                $(this).addClass('hovered');
            },
            function() {
                $(this).removeClass('hovered');
            }
        );

        // Package selection
        $('.pricing-card .btn').on('click', function(e) {
            var packageId = $(this).closest('.pricing-card').data('package-id');
            if (packageId) {
                // Store selected package in session storage
                sessionStorage.setItem('selectedPackage', packageId);
            }
        });
    }

    // Contact forms handling
    function initContactForms() {
        $('.contact-form, .support-form').on('submit', function(e) {
            e.preventDefault();
            
            var form = $(this);
            var formData = new FormData(this);
            formData.append('action', 'iptv_submit_form');
            formData.append('nonce', iptv_ajax.nonce);

            // Show loading state
            form.find('.submit-btn').prop('disabled', true).text('Sending...');

            $.ajax({
                url: iptv_ajax.ajax_url,
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    if (response.success) {
                        showNotification('Message sent successfully!', 'success');
                        form[0].reset();
                    } else {
                        showNotification(response.data || 'Error sending message', 'error');
                    }
                },
                error: function() {
                    showNotification('Network error. Please try again.', 'error');
                },
                complete: function() {
                    form.find('.submit-btn').prop('disabled', false).text('Send Message');
                }
            });
        });
    }

    // Customer dashboard functionality
    function initCustomerDashboard() {
        // Tab switching
        $('.dashboard-tabs .tab-link').on('click', function(e) {
            e.preventDefault();
            
            var targetTab = $(this).data('tab');
            
            // Update active tab
            $('.dashboard-tabs .tab-link').removeClass('active');
            $(this).addClass('active');
            
            // Show target content
            $('.tab-content').removeClass('active');
            $('#' + targetTab).addClass('active');
        });

        // Subscription management
        $('.renew-subscription').on('click', function(e) {
            e.preventDefault();
            
            var subscriptionId = $(this).data('subscription-id');
            
            if (confirm('Are you sure you want to renew this subscription?')) {
                renewSubscription(subscriptionId);
            }
        });

        $('.cancel-subscription').on('click', function(e) {
            e.preventDefault();
            
            var subscriptionId = $(this).data('subscription-id');
            
            if (confirm('Are you sure you want to cancel this subscription?')) {
                cancelSubscription(subscriptionId);
            }
        });
    }

    // Payment forms
    function initPaymentForms() {
        $('.payment-method').on('change', function() {
            var method = $(this).val();
            
            $('.payment-details').hide();
            $('#' + method + '-details').show();
        });

        $('.checkout-form').on('submit', function(e) {
            e.preventDefault();
            
            var form = $(this);
            var paymentMethod = form.find('input[name="payment_method"]:checked').val();
            
            if (!paymentMethod) {
                showNotification('Please select a payment method', 'error');
                return;
            }

            processPayment(form, paymentMethod);
        });
    }

    // Live chat functionality
    function initLiveChat() {
        $('#chat-toggle').on('click', function() {
            $('#chat-window').toggle();
        });

        $('#chat-close').on('click', function() {
            $('#chat-window').hide();
        });

        // Auto-hide chat on mobile scroll
        var lastScrollTop = 0;
        $(window).scroll(function() {
            var st = $(this).scrollTop();
            if (st > lastScrollTop && $(window).width() <= 768) {
                $('#live-chat-widget').fadeOut();
            } else {
                $('#live-chat-widget').fadeIn();
            }
            lastScrollTop = st;
        });
    }

    // Back to top button
    function initBackToTop() {
        $(window).scroll(function() {
            if ($(this).scrollTop() > 300) {
                $('#back-to-top').fadeIn();
            } else {
                $('#back-to-top').fadeOut();
            }
        });

        $('#back-to-top').on('click', function() {
            $('html, body').animate({scrollTop: 0}, 800);
        });
    }

    // Utility functions
    function showNotification(message, type) {
        var notification = $('<div class="notification notification-' + type + '">' + message + '</div>');
        
        $('body').append(notification);
        
        setTimeout(function() {
            notification.addClass('show');
        }, 100);
        
        setTimeout(function() {
            notification.removeClass('show');
            setTimeout(function() {
                notification.remove();
            }, 300);
        }, 3000);
    }

    function renewSubscription(subscriptionId) {
        $.ajax({
            url: iptv_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'iptv_renew_subscription',
                subscription_id: subscriptionId,
                nonce: iptv_ajax.nonce
            },
            success: function(response) {
                if (response.success) {
                    showNotification('Subscription renewed successfully!', 'success');
                    location.reload();
                } else {
                    showNotification(response.data || 'Error renewing subscription', 'error');
                }
            },
            error: function() {
                showNotification('Network error. Please try again.', 'error');
            }
        });
    }

    function cancelSubscription(subscriptionId) {
        $.ajax({
            url: iptv_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'iptv_cancel_subscription',
                subscription_id: subscriptionId,
                nonce: iptv_ajax.nonce
            },
            success: function(response) {
                if (response.success) {
                    showNotification('Subscription cancelled successfully!', 'success');
                    location.reload();
                } else {
                    showNotification(response.data || 'Error cancelling subscription', 'error');
                }
            },
            error: function() {
                showNotification('Network error. Please try again.', 'error');
            }
        });
    }

    function processPayment(form, paymentMethod) {
        var formData = new FormData(form[0]);
        formData.append('action', 'iptv_process_payment');
        formData.append('payment_method', paymentMethod);
        formData.append('nonce', iptv_ajax.nonce);

        // Show loading state
        form.find('.submit-btn').prop('disabled', true).text('Processing...');

        $.ajax({
            url: iptv_ajax.ajax_url,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    if (response.data.redirect_url) {
                        window.location.href = response.data.redirect_url;
                    } else {
                        showNotification('Payment processed successfully!', 'success');
                    }
                } else {
                    showNotification(response.data || 'Payment failed', 'error');
                }
            },
            error: function() {
                showNotification('Network error. Please try again.', 'error');
            },
            complete: function() {
                form.find('.submit-btn').prop('disabled', false).text('Complete Payment');
            }
        });
    }

    // Channel list filtering
    function initChannelFiltering() {
        $('.channel-filter').on('change', function() {
            var category = $(this).val();
            
            if (category === 'all') {
                $('.channel-item').show();
            } else {
                $('.channel-item').hide();
                $('.channel-item[data-category="' + category + '"]').show();
            }
        });

        $('.channel-search').on('input', function() {
            var searchTerm = $(this).val().toLowerCase();
            
            $('.channel-item').each(function() {
                var channelName = $(this).find('.channel-name').text().toLowerCase();
                if (channelName.includes(searchTerm)) {
                    $(this).show();
                } else {
                    $(this).hide();
                }
            });
        });
    }

    // Initialize channel filtering if on channels page
    if ($('.channels-page').length) {
        initChannelFiltering();
    }

})(jQuery);

// CSS for notifications
var notificationCSS = `
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 1rem 1.5rem;
    border-radius: 8px;
    color: white;
    font-weight: 500;
    z-index: 10000;
    transform: translateX(100%);
    transition: transform 0.3s ease;
}

.notification.show {
    transform: translateX(0);
}

.notification-success {
    background: #10b981;
}

.notification-error {
    background: #ef4444;
}

.notification-info {
    background: #3b82f6;
}
`;

// Inject notification CSS
var style = document.createElement('style');
style.textContent = notificationCSS;
document.head.appendChild(style);
