<?php
/**
 * Admin Dashboard Functions
 *
 * @package IPTV_Pro
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Add admin dashboard menu
 */
function iptv_pro_admin_menu() {
    // Main IPTV menu
    add_menu_page(
        __('IPTV Management', 'iptv-pro'),
        __('IPTV Pro', 'iptv-pro'),
        'manage_options',
        'iptv-dashboard',
        'iptv_pro_admin_dashboard_page',
        'dashicons-video-alt3',
        30
    );
    
    // Dashboard submenu
    add_submenu_page(
        'iptv-dashboard',
        __('Dashboard', 'iptv-pro'),
        __('Dashboard', 'iptv-pro'),
        'manage_options',
        'iptv-dashboard',
        'iptv_pro_admin_dashboard_page'
    );
    
    // Customers submenu
    add_submenu_page(
        'iptv-dashboard',
        __('Customers', 'iptv-pro'),
        __('Customers', 'iptv-pro'),
        'manage_options',
        'iptv-customers',
        'iptv_pro_customers_page'
    );
    
    // Subscriptions submenu
    add_submenu_page(
        'iptv-dashboard',
        __('Subscriptions', 'iptv-pro'),
        __('Subscriptions', 'iptv-pro'),
        'manage_options',
        'iptv-subscriptions',
        'iptv_pro_subscriptions_page'
    );
    
    // Reports submenu
    add_submenu_page(
        'iptv-dashboard',
        __('Reports', 'iptv-pro'),
        __('Reports', 'iptv-pro'),
        'manage_options',
        'iptv-reports',
        'iptv_pro_reports_page'
    );
    
    // Settings submenu
    add_submenu_page(
        'iptv-dashboard',
        __('IPTV Settings', 'iptv-pro'),
        __('Settings', 'iptv-pro'),
        'manage_options',
        'iptv-settings',
        'iptv_pro_settings_page'
    );
}
add_action('admin_menu', 'iptv_pro_admin_menu');

/**
 * Admin dashboard page
 */
function iptv_pro_admin_dashboard_page() {
    // Get statistics
    $total_customers = count(get_users());
    $active_subscriptions = count(get_users(array('meta_key' => 'subscription_status', 'meta_value' => 'active')));
    $total_packages = wp_count_posts('iptv_package')->publish;
    
    global $wpdb;
    $payments_table = $wpdb->prefix . 'iptv_payments';
    $monthly_revenue = $wpdb->get_var($wpdb->prepare(
        "SELECT SUM(amount) FROM $payments_table WHERE status = 'completed' AND created_at >= %s",
        date('Y-m-01')
    )) ?: 0;
    
    ?>
    <div class="wrap">
        <h1><?php _e('IPTV Pro Dashboard', 'iptv-pro'); ?></h1>
        
        <!-- Statistics Cards -->
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin: 20px 0;">
            <div class="iptv-stat-card">
                <h3><?php _e('Total Customers', 'iptv-pro'); ?></h3>
                <div class="stat-number"><?php echo number_format($total_customers); ?></div>
            </div>
            
            <div class="iptv-stat-card">
                <h3><?php _e('Active Subscriptions', 'iptv-pro'); ?></h3>
                <div class="stat-number"><?php echo number_format($active_subscriptions); ?></div>
            </div>
            
            <div class="iptv-stat-card">
                <h3><?php _e('IPTV Packages', 'iptv-pro'); ?></h3>
                <div class="stat-number"><?php echo number_format($total_packages); ?></div>
            </div>
            
            <div class="iptv-stat-card">
                <h3><?php _e('Monthly Revenue', 'iptv-pro'); ?></h3>
                <div class="stat-number">$<?php echo number_format($monthly_revenue, 2); ?></div>
            </div>
        </div>
        
        <!-- Quick Actions -->
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 20px 0;">
            <div class="iptv-admin-section">
                <h2><?php _e('Quick Actions', 'iptv-pro'); ?></h2>
                <div class="iptv-quick-actions">
                    <a href="<?php echo admin_url('post-new.php?post_type=iptv_package'); ?>" class="button button-primary">
                        <?php _e('Add New Package', 'iptv-pro'); ?>
                    </a>
                    <a href="<?php echo admin_url('admin.php?page=iptv-customers'); ?>" class="button">
                        <?php _e('Manage Customers', 'iptv-pro'); ?>
                    </a>
                    <a href="<?php echo admin_url('admin.php?page=iptv-payments'); ?>" class="button">
                        <?php _e('View Payments', 'iptv-pro'); ?>
                    </a>
                    <a href="<?php echo admin_url('admin.php?page=iptv-settings'); ?>" class="button">
                        <?php _e('Settings', 'iptv-pro'); ?>
                    </a>
                </div>
            </div>
            
            <div class="iptv-admin-section">
                <h2><?php _e('Recent Activity', 'iptv-pro'); ?></h2>
                <div class="iptv-recent-activity">
                    <?php
                    $recent_users = get_users(array('number' => 5, 'orderby' => 'registered', 'order' => 'DESC'));
                    if ($recent_users) {
                        echo '<ul>';
                        foreach ($recent_users as $user) {
                            echo '<li>' . sprintf(__('New customer: %s (%s)', 'iptv-pro'), $user->display_name, $user->user_email) . '</li>';
                        }
                        echo '</ul>';
                    } else {
                        echo '<p>' . __('No recent activity.', 'iptv-pro') . '</p>';
                    }
                    ?>
                </div>
            </div>
        </div>
        
        <!-- Charts Section -->
        <div class="iptv-admin-section">
            <h2><?php _e('Revenue Chart (Last 12 Months)', 'iptv-pro'); ?></h2>
            <canvas id="revenueChart" width="400" height="200"></canvas>
        </div>
    </div>
    
    <style>
    .iptv-stat-card {
        background: white;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        text-align: center;
    }
    
    .iptv-stat-card h3 {
        margin: 0 0 10px 0;
        color: #666;
        font-size: 14px;
        text-transform: uppercase;
    }
    
    .stat-number {
        font-size: 32px;
        font-weight: bold;
        color: #0073aa;
    }
    
    .iptv-admin-section {
        background: white;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .iptv-quick-actions {
        display: flex;
        flex-direction: column;
        gap: 10px;
    }
    
    .iptv-recent-activity ul {
        margin: 0;
        padding: 0;
        list-style: none;
    }
    
    .iptv-recent-activity li {
        padding: 8px 0;
        border-bottom: 1px solid #eee;
    }
    </style>
    
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
    // Revenue chart
    const ctx = document.getElementById('revenueChart').getContext('2d');
    const revenueChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
            datasets: [{
                label: 'Revenue ($)',
                data: [1200, 1900, 3000, 5000, 2000, 3000, 4500, 3200, 2800, 4100, 3900, 5200],
                borderColor: '#0073aa',
                backgroundColor: 'rgba(0, 115, 170, 0.1)',
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
    </script>
    <?php
}

/**
 * Customers management page
 */
function iptv_pro_customers_page() {
    $customers = get_users(array('orderby' => 'registered', 'order' => 'DESC'));
    
    ?>
    <div class="wrap">
        <h1><?php _e('Customer Management', 'iptv-pro'); ?></h1>
        
        <div class="tablenav top">
            <div class="alignleft actions">
                <select name="bulk_action">
                    <option value=""><?php _e('Bulk Actions', 'iptv-pro'); ?></option>
                    <option value="activate"><?php _e('Activate Subscription', 'iptv-pro'); ?></option>
                    <option value="suspend"><?php _e('Suspend Subscription', 'iptv-pro'); ?></option>
                    <option value="delete"><?php _e('Delete Customer', 'iptv-pro'); ?></option>
                </select>
                <input type="submit" class="button action" value="<?php _e('Apply', 'iptv-pro'); ?>">
            </div>
        </div>
        
        <table class="wp-list-table widefat fixed striped">
            <thead>
                <tr>
                    <td class="manage-column column-cb check-column">
                        <input type="checkbox" id="cb-select-all">
                    </td>
                    <th><?php _e('Customer', 'iptv-pro'); ?></th>
                    <th><?php _e('Email', 'iptv-pro'); ?></th>
                    <th><?php _e('Customer ID', 'iptv-pro'); ?></th>
                    <th><?php _e('Status', 'iptv-pro'); ?></th>
                    <th><?php _e('Package', 'iptv-pro'); ?></th>
                    <th><?php _e('Expires', 'iptv-pro'); ?></th>
                    <th><?php _e('Registered', 'iptv-pro'); ?></th>
                    <th><?php _e('Actions', 'iptv-pro'); ?></th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($customers as $customer) : 
                    $customer_id = get_user_meta($customer->ID, 'customer_id', true);
                    $status = get_user_meta($customer->ID, 'subscription_status', true);
                    $package_id = get_user_meta($customer->ID, 'package_type', true);
                    $end_date = get_user_meta($customer->ID, 'subscription_end_date', true);
                ?>
                    <tr>
                        <th class="check-column">
                            <input type="checkbox" name="customer[]" value="<?php echo $customer->ID; ?>">
                        </th>
                        <td>
                            <strong><?php echo esc_html($customer->display_name); ?></strong>
                        </td>
                        <td><?php echo esc_html($customer->user_email); ?></td>
                        <td><?php echo esc_html($customer_id); ?></td>
                        <td>
                            <?php
                            $status_colors = array(
                                'active' => '#10b981',
                                'expired' => '#ef4444',
                                'cancelled' => '#6b7280',
                                'suspended' => '#f59e0b',
                                'inactive' => '#9ca3af'
                            );
                            $color = $status_colors[$status] ?? '#6b7280';
                            ?>
                            <span style="color: <?php echo $color; ?>; font-weight: bold;">
                                <?php echo ucfirst($status ?: 'inactive'); ?>
                            </span>
                        </td>
                        <td>
                            <?php echo $package_id ? get_the_title($package_id) : '-'; ?>
                        </td>
                        <td>
                            <?php echo $end_date ? date('M j, Y', strtotime($end_date)) : '-'; ?>
                        </td>
                        <td><?php echo date('M j, Y', strtotime($customer->user_registered)); ?></td>
                        <td>
                            <a href="<?php echo admin_url('user-edit.php?user_id=' . $customer->ID); ?>" class="button button-small">
                                <?php _e('Edit', 'iptv-pro'); ?>
                            </a>
                        </td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>
    <?php
}

/**
 * Subscriptions management page
 */
function iptv_pro_subscriptions_page() {
    $subscriptions = get_posts(array(
        'post_type' => 'iptv_subscription',
        'posts_per_page' => -1,
        'post_status' => 'publish'
    ));
    
    ?>
    <div class="wrap">
        <h1><?php _e('Subscription Management', 'iptv-pro'); ?></h1>
        
        <table class="wp-list-table widefat fixed striped">
            <thead>
                <tr>
                    <th><?php _e('ID', 'iptv-pro'); ?></th>
                    <th><?php _e('Customer', 'iptv-pro'); ?></th>
                    <th><?php _e('Package', 'iptv-pro'); ?></th>
                    <th><?php _e('Start Date', 'iptv-pro'); ?></th>
                    <th><?php _e('End Date', 'iptv-pro'); ?></th>
                    <th><?php _e('Status', 'iptv-pro'); ?></th>
                    <th><?php _e('Actions', 'iptv-pro'); ?></th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($subscriptions as $subscription) : 
                    $customer_id = get_post_meta($subscription->ID, '_customer_id', true);
                    $package_id = get_post_meta($subscription->ID, '_package_id', true);
                    $start_date = get_post_meta($subscription->ID, '_start_date', true);
                    $end_date = get_post_meta($subscription->ID, '_end_date', true);
                    $status = get_post_meta($subscription->ID, '_subscription_status', true);
                    
                    $customer = get_userdata($customer_id);
                ?>
                    <tr>
                        <td><?php echo $subscription->ID; ?></td>
                        <td>
                            <?php echo $customer ? $customer->display_name : 'Unknown'; ?>
                            <?php if ($customer) : ?>
                                <br><small><?php echo $customer->user_email; ?></small>
                            <?php endif; ?>
                        </td>
                        <td><?php echo get_the_title($package_id); ?></td>
                        <td><?php echo $start_date ? date('M j, Y', strtotime($start_date)) : '-'; ?></td>
                        <td><?php echo $end_date ? date('M j, Y', strtotime($end_date)) : '-'; ?></td>
                        <td>
                            <?php
                            $status_colors = array(
                                'active' => '#10b981',
                                'expired' => '#ef4444',
                                'cancelled' => '#6b7280'
                            );
                            $color = $status_colors[$status] ?? '#6b7280';
                            ?>
                            <span style="color: <?php echo $color; ?>; font-weight: bold;">
                                <?php echo ucfirst($status); ?>
                            </span>
                        </td>
                        <td>
                            <a href="<?php echo admin_url('post.php?post=' . $subscription->ID . '&action=edit'); ?>" class="button button-small">
                                <?php _e('Edit', 'iptv-pro'); ?>
                            </a>
                        </td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>
    <?php
}

/**
 * Reports page
 */
function iptv_pro_reports_page() {
    global $wpdb;
    $payments_table = $wpdb->prefix . 'iptv_payments';
    
    // Get revenue data
    $monthly_revenue = $wpdb->get_results("
        SELECT 
            DATE_FORMAT(created_at, '%Y-%m') as month,
            SUM(amount) as revenue,
            COUNT(*) as transactions
        FROM $payments_table 
        WHERE status = 'completed' 
        AND created_at >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
        GROUP BY DATE_FORMAT(created_at, '%Y-%m')
        ORDER BY month
    ");
    
    ?>
    <div class="wrap">
        <h1><?php _e('Reports & Analytics', 'iptv-pro'); ?></h1>
        
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 20px 0;">
            <div class="iptv-admin-section">
                <h2><?php _e('Revenue Summary', 'iptv-pro'); ?></h2>
                <table class="wp-list-table widefat">
                    <thead>
                        <tr>
                            <th><?php _e('Month', 'iptv-pro'); ?></th>
                            <th><?php _e('Revenue', 'iptv-pro'); ?></th>
                            <th><?php _e('Transactions', 'iptv-pro'); ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($monthly_revenue as $data) : ?>
                            <tr>
                                <td><?php echo date('F Y', strtotime($data->month . '-01')); ?></td>
                                <td>$<?php echo number_format($data->revenue, 2); ?></td>
                                <td><?php echo $data->transactions; ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            
            <div class="iptv-admin-section">
                <h2><?php _e('Package Popularity', 'iptv-pro'); ?></h2>
                <?php
                $package_stats = $wpdb->get_results("
                    SELECT 
                        p.post_title as package_name,
                        COUNT(u.user_id) as subscribers
                    FROM {$wpdb->posts} p
                    LEFT JOIN {$wpdb->usermeta} u ON p.ID = u.meta_value AND u.meta_key = 'package_type'
                    WHERE p.post_type = 'iptv_package' AND p.post_status = 'publish'
                    GROUP BY p.ID
                    ORDER BY subscribers DESC
                ");
                ?>
                <table class="wp-list-table widefat">
                    <thead>
                        <tr>
                            <th><?php _e('Package', 'iptv-pro'); ?></th>
                            <th><?php _e('Subscribers', 'iptv-pro'); ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($package_stats as $stat) : ?>
                            <tr>
                                <td><?php echo esc_html($stat->package_name); ?></td>
                                <td><?php echo $stat->subscribers; ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <?php
}

/**
 * Settings page
 */
function iptv_pro_settings_page() {
    if (isset($_POST['submit'])) {
        // Save settings
        update_option('iptv_default_server_url', sanitize_url($_POST['default_server_url']));
        update_option('iptv_max_connections_default', intval($_POST['max_connections_default']));
        update_option('iptv_trial_duration', intval($_POST['trial_duration']));
        update_option('iptv_auto_suspend', isset($_POST['auto_suspend']));
        
        echo '<div class="notice notice-success"><p>' . __('Settings saved successfully!', 'iptv-pro') . '</p></div>';
    }
    
    $default_server_url = get_option('iptv_default_server_url', '');
    $max_connections_default = get_option('iptv_max_connections_default', 1);
    $trial_duration = get_option('iptv_trial_duration', 24);
    $auto_suspend = get_option('iptv_auto_suspend', false);
    
    ?>
    <div class="wrap">
        <h1><?php _e('IPTV Settings', 'iptv-pro'); ?></h1>
        
        <form method="post" action="">
            <table class="form-table">
                <tr>
                    <th scope="row"><?php _e('Default Server URL', 'iptv-pro'); ?></th>
                    <td>
                        <input type="url" name="default_server_url" value="<?php echo esc_attr($default_server_url); ?>" class="regular-text" />
                        <p class="description"><?php _e('Default IPTV server URL for new customers', 'iptv-pro'); ?></p>
                    </td>
                </tr>
                
                <tr>
                    <th scope="row"><?php _e('Default Max Connections', 'iptv-pro'); ?></th>
                    <td>
                        <input type="number" name="max_connections_default" value="<?php echo esc_attr($max_connections_default); ?>" min="1" max="10" />
                        <p class="description"><?php _e('Default number of simultaneous connections allowed', 'iptv-pro'); ?></p>
                    </td>
                </tr>
                
                <tr>
                    <th scope="row"><?php _e('Free Trial Duration', 'iptv-pro'); ?></th>
                    <td>
                        <input type="number" name="trial_duration" value="<?php echo esc_attr($trial_duration); ?>" min="1" max="168" />
                        <span><?php _e('hours', 'iptv-pro'); ?></span>
                        <p class="description"><?php _e('Duration of free trial in hours', 'iptv-pro'); ?></p>
                    </td>
                </tr>
                
                <tr>
                    <th scope="row"><?php _e('Auto-suspend expired subscriptions', 'iptv-pro'); ?></th>
                    <td>
                        <input type="checkbox" name="auto_suspend" value="1" <?php checked($auto_suspend, true); ?> />
                        <p class="description"><?php _e('Automatically suspend access when subscription expires', 'iptv-pro'); ?></p>
                    </td>
                </tr>
            </table>
            
            <?php submit_button(); ?>
        </form>
    </div>
    <?php
}

/**
 * Add admin styles
 */
function iptv_pro_admin_styles() {
    ?>
    <style>
    .iptv-admin-section {
        background: white;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        margin-bottom: 20px;
    }
    
    .iptv-admin-section h2 {
        margin-top: 0;
        border-bottom: 1px solid #eee;
        padding-bottom: 10px;
    }
    </style>
    <?php
}
add_action('admin_head', 'iptv_pro_admin_styles');

?>
