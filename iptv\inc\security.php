<?php
/**
 * Security Functions
 *
 * @package IPTV_Pro
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Enhanced login security
 */
function iptv_pro_login_security() {
    // Limit login attempts
    add_action('wp_login_failed', 'iptv_pro_login_failed');
    add_filter('authenticate', 'iptv_pro_check_login_attempts', 30, 3);
    
    // Add two-factor authentication option
    add_action('show_user_profile', 'iptv_pro_2fa_profile_fields');
    add_action('edit_user_profile', 'iptv_pro_2fa_profile_fields');
    add_action('personal_options_update', 'iptv_pro_save_2fa_profile_fields');
    add_action('edit_user_profile_update', 'iptv_pro_save_2fa_profile_fields');
}
add_action('init', 'iptv_pro_login_security');

/**
 * Track failed login attempts
 */
function iptv_pro_login_failed($username) {
    $ip = iptv_pro_get_user_ip();
    $attempts = get_transient('iptv_login_attempts_' . $ip) ?: 0;
    $attempts++;
    
    set_transient('iptv_login_attempts_' . $ip, $attempts, 15 * MINUTE_IN_SECONDS);
    
    // Log the attempt
    error_log("Failed login attempt for user: $username from IP: $ip (Attempt: $attempts)");
    
    // Block IP after 5 failed attempts
    if ($attempts >= 5) {
        set_transient('iptv_blocked_ip_' . $ip, true, HOUR_IN_SECONDS);
        error_log("IP blocked due to multiple failed login attempts: $ip");
    }
}

/**
 * Check login attempts before authentication
 */
function iptv_pro_check_login_attempts($user, $username, $password) {
    $ip = iptv_pro_get_user_ip();
    
    // Check if IP is blocked
    if (get_transient('iptv_blocked_ip_' . $ip)) {
        return new WP_Error('too_many_attempts', __('Too many failed login attempts. Please try again later.', 'iptv-pro'));
    }
    
    return $user;
}

/**
 * Get user IP address
 */
function iptv_pro_get_user_ip() {
    $ip_keys = array('HTTP_CLIENT_IP', 'HTTP_X_FORWARDED_FOR', 'REMOTE_ADDR');
    
    foreach ($ip_keys as $key) {
        if (array_key_exists($key, $_SERVER) === true) {
            foreach (explode(',', $_SERVER[$key]) as $ip) {
                $ip = trim($ip);
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) !== false) {
                    return $ip;
                }
            }
        }
    }
    
    return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
}

/**
 * Two-factor authentication profile fields
 */
function iptv_pro_2fa_profile_fields($user) {
    ?>
    <h3><?php _e('Two-Factor Authentication', 'iptv-pro'); ?></h3>
    <table class="form-table">
        <tr>
            <th><label for="enable_2fa"><?php _e('Enable 2FA', 'iptv-pro'); ?></label></th>
            <td>
                <input type="checkbox" name="enable_2fa" id="enable_2fa" value="1" <?php checked(get_user_meta($user->ID, 'enable_2fa', true), 1); ?> />
                <p class="description"><?php _e('Enable two-factor authentication for enhanced security', 'iptv-pro'); ?></p>
            </td>
        </tr>
        <tr>
            <th><label for="backup_email"><?php _e('Backup Email', 'iptv-pro'); ?></label></th>
            <td>
                <input type="email" name="backup_email" id="backup_email" value="<?php echo esc_attr(get_user_meta($user->ID, 'backup_email', true)); ?>" class="regular-text" />
                <p class="description"><?php _e('Alternative email for account recovery', 'iptv-pro'); ?></p>
            </td>
        </tr>
    </table>
    <?php
}

/**
 * Save 2FA profile fields
 */
function iptv_pro_save_2fa_profile_fields($user_id) {
    if (!current_user_can('edit_user', $user_id)) {
        return false;
    }

    if (isset($_POST['enable_2fa'])) {
        update_user_meta($user_id, 'enable_2fa', 1);
    } else {
        delete_user_meta($user_id, 'enable_2fa');
    }
    
    if (isset($_POST['backup_email'])) {
        update_user_meta($user_id, 'backup_email', sanitize_email($_POST['backup_email']));
    }
}

/**
 * Anti-piracy measures
 */
function iptv_pro_anti_piracy_init() {
    // Monitor concurrent connections
    add_action('wp_login', 'iptv_pro_track_user_session', 10, 2);
    add_action('wp_logout', 'iptv_pro_end_user_session');
    
    // Protect IPTV credentials
    add_filter('user_has_cap', 'iptv_pro_protect_iptv_credentials', 10, 3);
    
    // Monitor suspicious activity
    add_action('init', 'iptv_pro_monitor_suspicious_activity');
}
add_action('init', 'iptv_pro_anti_piracy_init');

/**
 * Track user sessions for concurrent connection monitoring
 */
function iptv_pro_track_user_session($user_login, $user) {
    $session_id = session_id() ?: wp_generate_password(32, false);
    $ip = iptv_pro_get_user_ip();
    $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
    
    $session_data = array(
        'session_id' => $session_id,
        'ip' => $ip,
        'user_agent' => $user_agent,
        'login_time' => current_time('mysql'),
        'last_activity' => current_time('mysql')
    );
    
    // Get existing sessions
    $sessions = get_user_meta($user->ID, 'active_sessions', true) ?: array();
    
    // Add new session
    $sessions[$session_id] = $session_data;
    
    // Check max connections
    $max_connections = get_user_meta($user->ID, 'max_connections', true) ?: 1;
    
    if (count($sessions) > $max_connections) {
        // Remove oldest session
        $oldest_session = array_reduce($sessions, function($oldest, $session) {
            return (!$oldest || strtotime($session['last_activity']) < strtotime($oldest['last_activity'])) ? $session : $oldest;
        });
        
        if ($oldest_session) {
            unset($sessions[$oldest_session['session_id']]);
            
            // Log the disconnection
            error_log("User {$user->ID} exceeded max connections. Disconnected session: {$oldest_session['session_id']}");
        }
    }
    
    update_user_meta($user->ID, 'active_sessions', $sessions);
    
    // Store current session ID
    update_user_meta($user->ID, 'current_session_id', $session_id);
}

/**
 * End user session
 */
function iptv_pro_end_user_session() {
    $user_id = get_current_user_id();
    if (!$user_id) return;
    
    $current_session = get_user_meta($user_id, 'current_session_id', true);
    $sessions = get_user_meta($user_id, 'active_sessions', true) ?: array();
    
    if ($current_session && isset($sessions[$current_session])) {
        unset($sessions[$current_session]);
        update_user_meta($user_id, 'active_sessions', $sessions);
    }
    
    delete_user_meta($user_id, 'current_session_id');
}

/**
 * Protect IPTV credentials from unauthorized access
 */
function iptv_pro_protect_iptv_credentials($allcaps, $caps, $args) {
    // Only allow access to own IPTV credentials
    if (isset($args[0]) && $args[0] === 'view_iptv_credentials') {
        $user_id = get_current_user_id();
        $target_user_id = isset($args[2]) ? $args[2] : 0;
        
        if ($user_id !== $target_user_id && !current_user_can('manage_options')) {
            $allcaps['view_iptv_credentials'] = false;
        }
    }
    
    return $allcaps;
}

/**
 * Monitor suspicious activity
 */
function iptv_pro_monitor_suspicious_activity() {
    if (!is_user_logged_in()) return;
    
    $user_id = get_current_user_id();
    $ip = iptv_pro_get_user_ip();
    $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
    
    // Check for unusual IP changes
    $last_ip = get_user_meta($user_id, 'last_login_ip', true);
    $last_user_agent = get_user_meta($user_id, 'last_user_agent', true);
    
    if ($last_ip && $last_ip !== $ip) {
        // IP changed - log and potentially alert
        $ip_changes = get_user_meta($user_id, 'ip_change_log', true) ?: array();
        $ip_changes[] = array(
            'old_ip' => $last_ip,
            'new_ip' => $ip,
            'time' => current_time('mysql'),
            'user_agent' => $user_agent
        );
        
        // Keep only last 10 changes
        $ip_changes = array_slice($ip_changes, -10);
        update_user_meta($user_id, 'ip_change_log', $ip_changes);
        
        // Alert if too many IP changes in short time
        $recent_changes = array_filter($ip_changes, function($change) {
            return strtotime($change['time']) > (time() - HOUR_IN_SECONDS);
        });
        
        if (count($recent_changes) > 3) {
            iptv_pro_alert_suspicious_activity($user_id, 'multiple_ip_changes', $ip_changes);
        }
    }
    
    // Update last known IP and user agent
    update_user_meta($user_id, 'last_login_ip', $ip);
    update_user_meta($user_id, 'last_user_agent', $user_agent);
    update_user_meta($user_id, 'last_activity', current_time('mysql'));
}

/**
 * Alert about suspicious activity
 */
function iptv_pro_alert_suspicious_activity($user_id, $activity_type, $details) {
    $user = get_userdata($user_id);
    if (!$user) return;
    
    // Log the activity
    error_log("Suspicious activity detected for user {$user_id}: {$activity_type}");
    
    // Send email alert to admin
    $admin_email = get_option('admin_email');
    $subject = sprintf(__('Suspicious Activity Alert - %s', 'iptv-pro'), get_bloginfo('name'));
    
    $message = sprintf(
        __('Suspicious activity detected for user: %s (ID: %d)
        
Activity Type: %s
User Email: %s
Time: %s
Details: %s

Please review this activity and take appropriate action if necessary.

Admin Dashboard: %s', 'iptv-pro'),
        $user->display_name,
        $user_id,
        $activity_type,
        $user->user_email,
        current_time('mysql'),
        print_r($details, true),
        admin_url('users.php?user_id=' . $user_id)
    );
    
    wp_mail($admin_email, $subject, $message);
    
    // Optionally suspend user if activity is severe
    if ($activity_type === 'multiple_ip_changes') {
        update_user_meta($user_id, 'account_flagged', true);
        update_user_meta($user_id, 'flag_reason', 'Suspicious IP activity');
        update_user_meta($user_id, 'flag_time', current_time('mysql'));
    }
}

/**
 * Secure AJAX endpoints
 */
function iptv_pro_secure_ajax_endpoints() {
    // Rate limiting for AJAX requests
    add_action('wp_ajax_iptv_process_payment', 'iptv_pro_rate_limit_ajax', 1);
    add_action('wp_ajax_iptv_submit_form', 'iptv_pro_rate_limit_ajax', 1);
    add_action('wp_ajax_nopriv_iptv_submit_form', 'iptv_pro_rate_limit_ajax', 1);
}
add_action('init', 'iptv_pro_secure_ajax_endpoints');

/**
 * Rate limit AJAX requests
 */
function iptv_pro_rate_limit_ajax() {
    $ip = iptv_pro_get_user_ip();
    $action = $_POST['action'] ?? '';
    
    $key = 'iptv_ajax_rate_limit_' . $ip . '_' . $action;
    $requests = get_transient($key) ?: 0;
    
    if ($requests >= 10) { // Max 10 requests per minute
        wp_die(__('Rate limit exceeded. Please try again later.', 'iptv-pro'));
    }
    
    set_transient($key, $requests + 1, MINUTE_IN_SECONDS);
}

/**
 * Content protection
 */
function iptv_pro_content_protection() {
    // Disable right-click on sensitive pages
    add_action('wp_footer', 'iptv_pro_disable_right_click');
    
    // Prevent hotlinking
    add_action('init', 'iptv_pro_prevent_hotlinking');
    
    // Add watermark to sensitive content
    add_filter('the_content', 'iptv_pro_add_content_watermark');
}
add_action('init', 'iptv_pro_content_protection');

/**
 * Disable right-click on sensitive pages
 */
function iptv_pro_disable_right_click() {
    if (is_page_template('page-templates/dashboard.php') || is_singular('iptv_package')) {
        ?>
        <script>
        document.addEventListener('contextmenu', function(e) {
            e.preventDefault();
        });
        
        document.addEventListener('keydown', function(e) {
            // Disable F12, Ctrl+Shift+I, Ctrl+U
            if (e.keyCode === 123 || 
                (e.ctrlKey && e.shiftKey && e.keyCode === 73) || 
                (e.ctrlKey && e.keyCode === 85)) {
                e.preventDefault();
            }
        });
        </script>
        <?php
    }
}

/**
 * Prevent hotlinking
 */
function iptv_pro_prevent_hotlinking() {
    if (isset($_SERVER['HTTP_REFERER'])) {
        $referer = parse_url($_SERVER['HTTP_REFERER'], PHP_URL_HOST);
        $host = $_SERVER['HTTP_HOST'];
        
        if ($referer && $referer !== $host) {
            // Check if accessing protected content
            $request_uri = $_SERVER['REQUEST_URI'];
            if (strpos($request_uri, '/wp-content/uploads/') !== false) {
                // Allow only logged-in users to access uploads
                if (!is_user_logged_in()) {
                    wp_die(__('Access denied', 'iptv-pro'));
                }
            }
        }
    }
}

/**
 * Add watermark to sensitive content
 */
function iptv_pro_add_content_watermark($content) {
    if (is_user_logged_in() && (is_singular('iptv_package') || is_page_template('page-templates/dashboard.php'))) {
        $user = wp_get_current_user();
        $watermark = sprintf(
            '<div style="position: fixed; bottom: 10px; right: 10px; background: rgba(0,0,0,0.7); color: white; padding: 5px 10px; font-size: 12px; border-radius: 4px; z-index: 9999; pointer-events: none;">%s - %s</div>',
            esc_html($user->user_email),
            date('Y-m-d H:i')
        );
        $content .= $watermark;
    }
    
    return $content;
}

/**
 * Security headers
 */
function iptv_pro_security_headers() {
    if (!is_admin()) {
        header('X-Content-Type-Options: nosniff');
        header('X-Frame-Options: SAMEORIGIN');
        header('X-XSS-Protection: 1; mode=block');
        header('Referrer-Policy: strict-origin-when-cross-origin');
        
        // Content Security Policy for sensitive pages
        if (is_page_template('page-templates/dashboard.php') || is_page_template('page-templates/checkout.php')) {
            header("Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline' https://www.paypal.com https://js.stripe.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com;");
        }
    }
}
add_action('send_headers', 'iptv_pro_security_headers');

/**
 * Clean up expired sessions
 */
function iptv_pro_cleanup_expired_sessions() {
    $users = get_users(array('meta_key' => 'active_sessions'));
    
    foreach ($users as $user) {
        $sessions = get_user_meta($user->ID, 'active_sessions', true);
        if (!is_array($sessions)) continue;
        
        $active_sessions = array();
        foreach ($sessions as $session_id => $session_data) {
            // Remove sessions older than 24 hours
            if (strtotime($session_data['last_activity']) > (time() - DAY_IN_SECONDS)) {
                $active_sessions[$session_id] = $session_data;
            }
        }
        
        update_user_meta($user->ID, 'active_sessions', $active_sessions);
    }
}

// Schedule cleanup
if (!wp_next_scheduled('iptv_pro_cleanup_sessions')) {
    wp_schedule_event(time(), 'hourly', 'iptv_pro_cleanup_sessions');
}
add_action('iptv_pro_cleanup_sessions', 'iptv_pro_cleanup_expired_sessions');

?>
