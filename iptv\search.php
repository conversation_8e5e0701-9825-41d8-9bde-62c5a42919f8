<?php
/**
 * The template for displaying search results pages
 *
 * @package IPTV_Pro
 */

get_header(); ?>

<main class="main-content search-results-page">
    <div class="container" style="padding: 2rem 0;">
        
        <!-- Search Results Header -->
        <div style="margin-bottom: 3rem;">
            <h1 style="font-size: 2.5rem; font-weight: 700; margin-bottom: 1rem; color: #1f2937;">
                <?php
                printf(
                    __('Search Results for: %s', 'iptv-pro'),
                    '<span style="color: #3b82f6;">"' . get_search_query() . '"</span>'
                );
                ?>
            </h1>
            
            <?php if (have_posts()) : ?>
                <p style="color: #6b7280; font-size: 1.125rem;">
                    <?php
                    global $wp_query;
                    printf(
                        _n(
                            'Found %d result',
                            'Found %d results',
                            $wp_query->found_posts,
                            'iptv-pro'
                        ),
                        $wp_query->found_posts
                    );
                    ?>
                </p>
            <?php endif; ?>
        </div>

        <!-- Search Form -->
        <div style="background: white; padding: 2rem; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.05); margin-bottom: 3rem;">
            <h2 style="font-size: 1.25rem; font-weight: 600; margin-bottom: 1rem; color: #1f2937;">
                <?php _e('Refine Your Search', 'iptv-pro'); ?>
            </h2>
            <form role="search" method="get" action="<?php echo esc_url(home_url('/')); ?>" style="display: flex; gap: 1rem;">
                <input type="search" name="s" value="<?php echo esc_attr(get_search_query()); ?>" placeholder="<?php _e('Search for packages, support, or information...', 'iptv-pro'); ?>" style="flex: 1; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 6px; font-size: 1rem;" />
                <button type="submit" class="btn btn-primary" style="padding: 0.75rem 1.5rem;">
                    <?php _e('Search', 'iptv-pro'); ?>
                </button>
            </form>
        </div>

        <div style="display: grid; grid-template-columns: 2fr 1fr; gap: 3rem;">
            
            <!-- Search Results -->
            <div class="search-results">
                <?php if (have_posts()) : ?>
                    <div style="display: grid; gap: 2rem;">
                        <?php while (have_posts()) : the_post(); ?>
                            <article style="background: white; padding: 2rem; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.05);">
                                
                                <!-- Post Type Badge -->
                                <div style="margin-bottom: 1rem;">
                                    <?php
                                    $post_type = get_post_type();
                                    $post_type_colors = array(
                                        'iptv_package' => '#3b82f6',
                                        'post' => '#10b981',
                                        'page' => '#f59e0b',
                                        'support_ticket' => '#ef4444'
                                    );
                                    $color = $post_type_colors[$post_type] ?? '#6b7280';
                                    
                                    $post_type_labels = array(
                                        'iptv_package' => __('IPTV Package', 'iptv-pro'),
                                        'post' => __('Blog Post', 'iptv-pro'),
                                        'page' => __('Page', 'iptv-pro'),
                                        'support_ticket' => __('Support', 'iptv-pro')
                                    );
                                    $label = $post_type_labels[$post_type] ?? ucfirst($post_type);
                                    ?>
                                    <span style="background: <?php echo $color; ?>; color: white; padding: 0.25rem 0.75rem; border-radius: 20px; font-size: 0.75rem; font-weight: 600;">
                                        <?php echo $label; ?>
                                    </span>
                                </div>
                                
                                <!-- Post Title -->
                                <h2 style="font-size: 1.5rem; font-weight: 600; margin-bottom: 1rem;">
                                    <a href="<?php the_permalink(); ?>" style="color: #1f2937; text-decoration: none;">
                                        <?php the_title(); ?>
                                    </a>
                                </h2>
                                
                                <!-- Post Meta -->
                                <div style="display: flex; align-items: center; gap: 1rem; margin-bottom: 1rem; color: #6b7280; font-size: 0.875rem;">
                                    <span><?php echo get_the_date(); ?></span>
                                    <?php if (get_post_type() === 'post') : ?>
                                        <span>•</span>
                                        <span><?php _e('By', 'iptv-pro'); ?> <?php the_author(); ?></span>
                                    <?php endif; ?>
                                    
                                    <?php if (get_post_type() === 'iptv_package') : ?>
                                        <?php
                                        $price = get_post_meta(get_the_ID(), '_iptv_price', true);
                                        $channels = get_post_meta(get_the_ID(), '_iptv_channels', true);
                                        if ($price || $channels) :
                                        ?>
                                            <span>•</span>
                                            <?php if ($price) : ?>
                                                <span style="color: #3b82f6; font-weight: 600;">$<?php echo esc_html($price); ?></span>
                                            <?php endif; ?>
                                            <?php if ($channels) : ?>
                                                <span>•</span>
                                                <span><?php echo esc_html($channels); ?>+ <?php _e('channels', 'iptv-pro'); ?></span>
                                            <?php endif; ?>
                                        <?php endif; ?>
                                    <?php endif; ?>
                                </div>
                                
                                <!-- Post Excerpt -->
                                <div style="color: #4b5563; line-height: 1.6; margin-bottom: 1.5rem;">
                                    <?php
                                    if (has_excerpt()) {
                                        the_excerpt();
                                    } else {
                                        echo wp_trim_words(get_the_content(), 30, '...');
                                    }
                                    ?>
                                </div>
                                
                                <!-- Read More -->
                                <div style="display: flex; justify-content: space-between; align-items: center;">
                                    <a href="<?php the_permalink(); ?>" class="btn btn-primary" style="font-size: 0.875rem; padding: 0.5rem 1rem;">
                                        <?php
                                        if (get_post_type() === 'iptv_package') {
                                            _e('View Package', 'iptv-pro');
                                        } else {
                                            _e('Read More', 'iptv-pro');
                                        }
                                        ?>
                                    </a>
                                    
                                    <?php if (get_post_type() === 'iptv_package') : ?>
                                        <a href="<?php echo add_query_arg('package', get_the_ID(), get_permalink(get_page_by_path('checkout'))); ?>" class="btn btn-secondary" style="font-size: 0.875rem; padding: 0.5rem 1rem;">
                                            <?php _e('Order Now', 'iptv-pro'); ?>
                                        </a>
                                    <?php endif; ?>
                                </div>
                            </article>
                        <?php endwhile; ?>
                    </div>
                    
                    <!-- Pagination -->
                    <div style="margin-top: 3rem;">
                        <?php
                        the_posts_pagination(array(
                            'mid_size' => 2,
                            'prev_text' => __('← Previous', 'iptv-pro'),
                            'next_text' => __('Next →', 'iptv-pro'),
                        ));
                        ?>
                    </div>
                    
                <?php else : ?>
                    <!-- No Results -->
                    <div style="background: white; padding: 3rem; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.05); text-align: center;">
                        <div style="font-size: 4rem; margin-bottom: 1rem;">🔍</div>
                        <h2 style="font-size: 1.5rem; font-weight: 600; margin-bottom: 1rem; color: #1f2937;">
                            <?php _e('No Results Found', 'iptv-pro'); ?>
                        </h2>
                        <p style="color: #6b7280; margin-bottom: 2rem;">
                            <?php printf(__('Sorry, no results were found for "%s". Try searching with different keywords.', 'iptv-pro'), get_search_query()); ?>
                        </p>
                        
                        <!-- Search Suggestions -->
                        <div style="background: #f8fafc; padding: 1.5rem; border-radius: 8px; margin-bottom: 2rem;">
                            <h3 style="font-size: 1.125rem; font-weight: 600; margin-bottom: 1rem; color: #374151;">
                                <?php _e('Search Suggestions:', 'iptv-pro'); ?>
                            </h3>
                            <ul style="list-style: none; padding: 0; color: #6b7280;">
                                <li style="margin-bottom: 0.5rem;">• <?php _e('Check your spelling', 'iptv-pro'); ?></li>
                                <li style="margin-bottom: 0.5rem;">• <?php _e('Try more general keywords', 'iptv-pro'); ?></li>
                                <li style="margin-bottom: 0.5rem;">• <?php _e('Use fewer keywords', 'iptv-pro'); ?></li>
                                <li>• <?php _e('Browse our popular pages below', 'iptv-pro'); ?></li>
                            </ul>
                        </div>
                        
                        <a href="<?php echo home_url('/'); ?>" class="btn btn-primary">
                            <?php _e('Back to Homepage', 'iptv-pro'); ?>
                        </a>
                    </div>
                <?php endif; ?>
            </div>
            
            <!-- Sidebar -->
            <aside class="search-sidebar">
                
                <!-- Popular Packages -->
                <?php
                $popular_packages = new WP_Query(array(
                    'post_type' => 'iptv_package',
                    'posts_per_page' => 3,
                    'meta_key' => '_iptv_featured',
                    'meta_value' => '1'
                ));
                
                if ($popular_packages->have_posts()) : ?>
                    <div style="background: white; padding: 2rem; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.05); margin-bottom: 2rem;">
                        <h3 style="font-size: 1.25rem; font-weight: 600; margin-bottom: 1.5rem; color: #1f2937;">
                            <?php _e('Popular Packages', 'iptv-pro'); ?>
                        </h3>
                        
                        <div style="display: grid; gap: 1rem;">
                            <?php while ($popular_packages->have_posts()) : $popular_packages->the_post();
                                $price = get_post_meta(get_the_ID(), '_iptv_price', true);
                            ?>
                                <div style="border: 1px solid #e5e7eb; border-radius: 8px; padding: 1rem;">
                                    <h4 style="font-size: 1rem; font-weight: 600; margin-bottom: 0.5rem;">
                                        <a href="<?php the_permalink(); ?>" style="color: #1f2937; text-decoration: none;">
                                            <?php the_title(); ?>
                                        </a>
                                    </h4>
                                    <div style="color: #3b82f6; font-weight: 600; margin-bottom: 0.5rem;">
                                        $<?php echo esc_html($price); ?>
                                    </div>
                                    <a href="<?php the_permalink(); ?>" style="color: #3b82f6; text-decoration: none; font-size: 0.875rem;">
                                        <?php _e('View Details →', 'iptv-pro'); ?>
                                    </a>
                                </div>
                            <?php endwhile; ?>
                        </div>
                    </div>
                    <?php wp_reset_postdata(); ?>
                <?php endif; ?>
                
                <!-- Quick Links -->
                <div style="background: white; padding: 2rem; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.05); margin-bottom: 2rem;">
                    <h3 style="font-size: 1.25rem; font-weight: 600; margin-bottom: 1.5rem; color: #1f2937;">
                        <?php _e('Quick Links', 'iptv-pro'); ?>
                    </h3>
                    
                    <div style="display: grid; gap: 0.75rem;">
                        <a href="<?php echo get_permalink(get_page_by_path('packages')); ?>" style="color: #3b82f6; text-decoration: none; padding: 0.5rem 0; border-bottom: 1px solid #f3f4f6;">
                            📦 <?php _e('All Packages', 'iptv-pro'); ?>
                        </a>
                        <a href="<?php echo get_permalink(get_page_by_path('support')); ?>" style="color: #3b82f6; text-decoration: none; padding: 0.5rem 0; border-bottom: 1px solid #f3f4f6;">
                            🛠️ <?php _e('Support Center', 'iptv-pro'); ?>
                        </a>
                        <a href="<?php echo get_permalink(get_page_by_path('free-trial')); ?>" style="color: #3b82f6; text-decoration: none; padding: 0.5rem 0; border-bottom: 1px solid #f3f4f6;">
                            🆓 <?php _e('Free Trial', 'iptv-pro'); ?>
                        </a>
                        <a href="<?php echo get_permalink(get_page_by_path('contact')); ?>" style="color: #3b82f6; text-decoration: none; padding: 0.5rem 0;">
                            📞 <?php _e('Contact Us', 'iptv-pro'); ?>
                        </a>
                    </div>
                </div>
                
                <!-- Help Section -->
                <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 2rem; border-radius: 12px; text-align: center;">
                    <h3 style="font-size: 1.25rem; font-weight: 600; margin-bottom: 1rem;">
                        <?php _e('Need Help?', 'iptv-pro'); ?>
                    </h3>
                    <p style="margin-bottom: 1.5rem; opacity: 0.9; font-size: 0.875rem;">
                        <?php _e('Can\'t find what you\'re looking for? Our support team is here to help!', 'iptv-pro'); ?>
                    </p>
                    <a href="<?php echo get_permalink(get_page_by_path('support')); ?>" class="btn btn-primary" style="background: white; color: #3b82f6; font-size: 0.875rem; padding: 0.5rem 1rem;">
                        <?php _e('Get Support', 'iptv-pro'); ?>
                    </a>
                </div>
            </aside>
        </div>
    </div>
</main>

<style>
@media (max-width: 768px) {
    .search-results-page .container > div:last-child {
        grid-template-columns: 1fr !important;
    }
    
    .search-sidebar {
        order: -1;
    }
    
    .search-results-page form {
        flex-direction: column !important;
    }
    
    .search-results-page form button {
        width: 100%;
    }
}
</style>

<?php get_footer(); ?>
