<?php
/**
 * The main template file
 *
 * @package IPTV_Pro
 */

get_header(); ?>

<main class="main-content">
    <?php if (is_home() || is_front_page()) : ?>
        <!-- Hero Section -->
        <section class="hero-section">
            <div class="container">
                <div class="hero-content">
                    <h1><?php echo get_theme_mod('hero_title', __('Premium IPTV Services', 'iptv-pro')); ?></h1>
                    <p><?php echo get_theme_mod('hero_subtitle', __('Experience unlimited entertainment with our high-quality IPTV packages. Stream thousands of channels in HD and 4K quality.', 'iptv-pro')); ?></p>
                    <div class="hero-actions">
                        <a href="<?php echo get_permalink(get_page_by_path('packages')); ?>" class="btn btn-primary">
                            <?php _e('View Packages', 'iptv-pro'); ?>
                        </a>
                        <a href="<?php echo get_permalink(get_page_by_path('free-trial')); ?>" class="btn btn-secondary">
                            <?php _e('Free Trial', 'iptv-pro'); ?>
                        </a>
                    </div>
                </div>
            </div>
        </section>

        <!-- Features Section -->
        <section class="features-section">
            <div class="container">
                <h2 class="section-title"><?php _e('Why Choose Our IPTV Service?', 'iptv-pro'); ?></h2>
                <div class="features-grid">
                    <div class="feature-card">
                        <div class="feature-icon">📺</div>
                        <h3><?php _e('Premium Channels', 'iptv-pro'); ?></h3>
                        <p><?php _e('Access to thousands of premium channels from around the world in HD and 4K quality.', 'iptv-pro'); ?></p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">⚡</div>
                        <h3><?php _e('High-Speed Streaming', 'iptv-pro'); ?></h3>
                        <p><?php _e('Lightning-fast streaming with minimal buffering and maximum uptime guarantee.', 'iptv-pro'); ?></p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">📱</div>
                        <h3><?php _e('Multi-Device Support', 'iptv-pro'); ?></h3>
                        <p><?php _e('Watch on any device - Smart TV, smartphone, tablet, computer, or streaming device.', 'iptv-pro'); ?></p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">🔒</div>
                        <h3><?php _e('Secure & Reliable', 'iptv-pro'); ?></h3>
                        <p><?php _e('Advanced encryption and secure servers ensure your privacy and uninterrupted service.', 'iptv-pro'); ?></p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">🎬</div>
                        <h3><?php _e('VOD Library', 'iptv-pro'); ?></h3>
                        <p><?php _e('Extensive video-on-demand library with latest movies and TV series.', 'iptv-pro'); ?></p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">🛠️</div>
                        <h3><?php _e('24/7 Support', 'iptv-pro'); ?></h3>
                        <p><?php _e('Round-the-clock customer support to help you with any issues or questions.', 'iptv-pro'); ?></p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Pricing Section -->
        <section class="pricing-section">
            <div class="container">
                <h2 class="section-title"><?php _e('Choose Your Package', 'iptv-pro'); ?></h2>
                <div class="pricing-grid">
                    <?php
                    $packages = new WP_Query(array(
                        'post_type' => 'iptv_package',
                        'posts_per_page' => 6,
                        'meta_key' => '_iptv_price',
                        'orderby' => 'meta_value_num',
                        'order' => 'ASC'
                    ));
                    
                    if ($packages->have_posts()) :
                        while ($packages->have_posts()) : $packages->the_post();
                            $price = get_post_meta(get_the_ID(), '_iptv_price', true);
                            $duration = get_post_meta(get_the_ID(), '_iptv_duration', true);
                            $channels = get_post_meta(get_the_ID(), '_iptv_channels', true);
                            $features = get_post_meta(get_the_ID(), '_iptv_features', true);
                            $is_featured = get_post_meta(get_the_ID(), '_iptv_featured', true);
                            ?>
                            <div class="pricing-card <?php echo $is_featured ? 'featured' : ''; ?>">
                                <?php if ($is_featured) : ?>
                                    <div class="pricing-badge"><?php _e('Most Popular', 'iptv-pro'); ?></div>
                                <?php endif; ?>
                                <div class="pricing-header">
                                    <h3 class="pricing-title"><?php the_title(); ?></h3>
                                    <div class="pricing-price">$<?php echo esc_html($price); ?></div>
                                    <div class="pricing-period"><?php printf(__('per %d months', 'iptv-pro'), $duration); ?></div>
                                </div>
                                <ul class="pricing-features">
                                    <li><?php printf(__('%s+ Channels', 'iptv-pro'), $channels); ?></li>
                                    <?php
                                    if ($features) {
                                        $feature_list = explode("\n", $features);
                                        foreach ($feature_list as $feature) {
                                            if (trim($feature)) {
                                                echo '<li>' . esc_html(trim($feature)) . '</li>';
                                            }
                                        }
                                    }
                                    ?>
                                </ul>
                                <a href="<?php echo add_query_arg('package', get_the_ID(), get_permalink(get_page_by_path('checkout'))); ?>" class="btn btn-primary">
                                    <?php _e('Get Started', 'iptv-pro'); ?>
                                </a>
                            </div>
                            <?php
                        endwhile;
                        wp_reset_postdata();
                    endif;
                    ?>
                </div>
            </div>
        </section>

        <!-- Testimonials Section -->
        <section class="testimonials-section" style="padding: 6rem 0; background: white;">
            <div class="container">
                <h2 class="section-title"><?php _e('What Our Customers Say', 'iptv-pro'); ?></h2>
                <div class="testimonials-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem; margin-top: 3rem;">
                    <div class="testimonial-card" style="background: #f8fafc; padding: 2rem; border-radius: 12px; text-align: center;">
                        <p style="font-style: italic; margin-bottom: 1rem; color: #4b5563;">"Excellent service with crystal clear quality. The customer support is outstanding!"</p>
                        <div style="font-weight: 600; color: #1f2937;">- John D.</div>
                    </div>
                    <div class="testimonial-card" style="background: #f8fafc; padding: 2rem; border-radius: 12px; text-align: center;">
                        <p style="font-style: italic; margin-bottom: 1rem; color: #4b5563;">"Best IPTV service I've used. Great channel selection and reliable streaming."</p>
                        <div style="font-weight: 600; color: #1f2937;">- Sarah M.</div>
                    </div>
                    <div class="testimonial-card" style="background: #f8fafc; padding: 2rem; border-radius: 12px; text-align: center;">
                        <p style="font-style: italic; margin-bottom: 1rem; color: #4b5563;">"Professional service with competitive pricing. Highly recommended!"</p>
                        <div style="font-weight: 600; color: #1f2937;">- Mike R.</div>
                    </div>
                </div>
            </div>
        </section>

    <?php else : ?>
        <!-- Regular blog/archive content -->
        <div class="container" style="padding: 2rem 0;">
            <div class="content-area">
                <?php if (have_posts()) : ?>
                    <header class="page-header" style="margin-bottom: 3rem;">
                        <?php
                        if (is_home()) {
                            echo '<h1 class="page-title">' . __('Latest News', 'iptv-pro') . '</h1>';
                        } elseif (is_archive()) {
                            the_archive_title('<h1 class="page-title">', '</h1>');
                            the_archive_description('<div class="archive-description">', '</div>');
                        } elseif (is_search()) {
                            echo '<h1 class="page-title">' . sprintf(__('Search Results for: %s', 'iptv-pro'), get_search_query()) . '</h1>';
                        }
                        ?>
                    </header>

                    <div class="posts-grid" style="display: grid; gap: 2rem;">
                        <?php while (have_posts()) : the_post(); ?>
                            <article id="post-<?php the_ID(); ?>" <?php post_class('post-card'); ?> style="background: white; padding: 2rem; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.05);">
                                <?php if (has_post_thumbnail()) : ?>
                                    <div class="post-thumbnail" style="margin-bottom: 1.5rem;">
                                        <a href="<?php the_permalink(); ?>">
                                            <?php the_post_thumbnail('iptv-featured', array('style' => 'width: 100%; height: 200px; object-fit: cover; border-radius: 8px;')); ?>
                                        </a>
                                    </div>
                                <?php endif; ?>
                                
                                <header class="entry-header" style="margin-bottom: 1rem;">
                                    <h2 class="entry-title" style="margin-bottom: 0.5rem;">
                                        <a href="<?php the_permalink(); ?>" style="color: #1f2937; text-decoration: none; font-weight: 600;">
                                            <?php the_title(); ?>
                                        </a>
                                    </h2>
                                    <div class="entry-meta" style="color: #6b7280; font-size: 0.875rem;">
                                        <?php echo get_the_date(); ?> | <?php the_author(); ?>
                                    </div>
                                </header>
                                
                                <div class="entry-content" style="color: #4b5563; line-height: 1.6;">
                                    <?php the_excerpt(); ?>
                                </div>
                                
                                <footer class="entry-footer" style="margin-top: 1.5rem;">
                                    <a href="<?php the_permalink(); ?>" class="read-more" style="color: #3b82f6; text-decoration: none; font-weight: 500;">
                                        <?php _e('Read More', 'iptv-pro'); ?> →
                                    </a>
                                </footer>
                            </article>
                        <?php endwhile; ?>
                    </div>

                    <?php
                    // Pagination
                    the_posts_pagination(array(
                        'mid_size' => 2,
                        'prev_text' => __('← Previous', 'iptv-pro'),
                        'next_text' => __('Next →', 'iptv-pro'),
                    ));
                    ?>

                <?php else : ?>
                    <div class="no-content" style="text-align: center; padding: 4rem 0;">
                        <h2><?php _e('Nothing Found', 'iptv-pro'); ?></h2>
                        <p><?php _e('It looks like nothing was found at this location. Maybe try a search?', 'iptv-pro'); ?></p>
                        <?php get_search_form(); ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    <?php endif; ?>
</main>

<?php get_footer(); ?>
