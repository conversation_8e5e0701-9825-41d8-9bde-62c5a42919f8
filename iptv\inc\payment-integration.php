<?php
/**
 * Payment Integration Functions
 *
 * @package IPTV_Pro
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Process payment AJAX handler
 */
function iptv_pro_process_payment() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'iptv_nonce')) {
        wp_die(__('Security check failed', 'iptv-pro'));
    }

    $payment_method = sanitize_text_field($_POST['payment_method']);
    $package_id = intval($_POST['package_id']);
    $user_id = get_current_user_id();

    if (!$user_id) {
        wp_send_json_error(__('Please log in to continue', 'iptv-pro'));
    }

    // Get package details
    $package = get_post($package_id);
    if (!$package || $package->post_type !== 'iptv_package') {
        wp_send_json_error(__('Invalid package selected', 'iptv-pro'));
    }

    $price = get_post_meta($package_id, '_iptv_price', true);
    $duration = get_post_meta($package_id, '_iptv_duration', true);

    // Process payment based on method
    switch ($payment_method) {
        case 'paypal':
            $result = iptv_pro_process_paypal_payment($package_id, $price, $user_id);
            break;
        case 'stripe':
            $result = iptv_pro_process_stripe_payment($package_id, $price, $user_id);
            break;
        case 'crypto':
            $result = iptv_pro_process_crypto_payment($package_id, $price, $user_id);
            break;
        default:
            wp_send_json_error(__('Invalid payment method', 'iptv-pro'));
    }

    if ($result['success']) {
        wp_send_json_success($result);
    } else {
        wp_send_json_error($result['message']);
    }
}
add_action('wp_ajax_iptv_process_payment', 'iptv_pro_process_payment');
add_action('wp_ajax_nopriv_iptv_process_payment', 'iptv_pro_process_payment');

/**
 * Process PayPal payment
 */
function iptv_pro_process_paypal_payment($package_id, $amount, $user_id) {
    if (!get_theme_mod('enable_paypal', true)) {
        return array('success' => false, 'message' => __('PayPal is not enabled', 'iptv-pro'));
    }

    $client_id = get_theme_mod('paypal_client_id');
    if (!$client_id) {
        return array('success' => false, 'message' => __('PayPal not configured', 'iptv-pro'));
    }

    // Create PayPal order
    $order_data = array(
        'intent' => 'CAPTURE',
        'purchase_units' => array(
            array(
                'amount' => array(
                    'currency_code' => 'USD',
                    'value' => number_format($amount, 2, '.', '')
                ),
                'description' => get_the_title($package_id)
            )
        ),
        'application_context' => array(
            'return_url' => add_query_arg(array(
                'payment_success' => '1',
                'package_id' => $package_id,
                'user_id' => $user_id
            ), home_url('/payment-success/')),
            'cancel_url' => home_url('/payment-cancelled/')
        )
    );

    // In a real implementation, you would make an API call to PayPal here
    // For demo purposes, we'll simulate a successful response
    $paypal_order_id = 'PAYPAL_' . time() . '_' . $user_id;
    
    // Store payment record
    iptv_pro_create_payment_record($user_id, $package_id, $amount, 'paypal', 'pending', $paypal_order_id);

    return array(
        'success' => true,
        'redirect_url' => 'https://www.sandbox.paypal.com/checkoutnow?token=' . $paypal_order_id
    );
}

/**
 * Process Stripe payment
 */
function iptv_pro_process_stripe_payment($package_id, $amount, $user_id) {
    if (!get_theme_mod('enable_stripe', true)) {
        return array('success' => false, 'message' => __('Stripe is not enabled', 'iptv-pro'));
    }

    $publishable_key = get_theme_mod('stripe_publishable_key');
    if (!$publishable_key) {
        return array('success' => false, 'message' => __('Stripe not configured', 'iptv-pro'));
    }

    // In a real implementation, you would use Stripe's PHP SDK here
    // For demo purposes, we'll simulate a successful response
    $stripe_session_id = 'STRIPE_' . time() . '_' . $user_id;
    
    // Store payment record
    iptv_pro_create_payment_record($user_id, $package_id, $amount, 'stripe', 'pending', $stripe_session_id);

    return array(
        'success' => true,
        'redirect_url' => add_query_arg(array(
            'payment_success' => '1',
            'package_id' => $package_id,
            'user_id' => $user_id,
            'session_id' => $stripe_session_id
        ), home_url('/payment-success/'))
    );
}

/**
 * Process cryptocurrency payment
 */
function iptv_pro_process_crypto_payment($package_id, $amount, $user_id) {
    if (!get_theme_mod('enable_crypto', false)) {
        return array('success' => false, 'message' => __('Cryptocurrency payments are not enabled', 'iptv-pro'));
    }

    // Generate crypto payment address (demo)
    $crypto_address = '******************************************';
    $crypto_amount = $amount; // In a real implementation, convert USD to crypto
    
    // Store payment record
    $payment_id = iptv_pro_create_payment_record($user_id, $package_id, $amount, 'crypto', 'pending');

    return array(
        'success' => true,
        'crypto_address' => $crypto_address,
        'crypto_amount' => $crypto_amount,
        'payment_id' => $payment_id
    );
}

/**
 * Create payment record
 */
function iptv_pro_create_payment_record($user_id, $package_id, $amount, $method, $status, $transaction_id = null) {
    global $wpdb;
    
    $table_name = $wpdb->prefix . 'iptv_payments';
    
    // Create table if it doesn't exist
    iptv_pro_create_payments_table();
    
    $payment_data = array(
        'user_id' => $user_id,
        'package_id' => $package_id,
        'amount' => $amount,
        'payment_method' => $method,
        'status' => $status,
        'transaction_id' => $transaction_id,
        'created_at' => current_time('mysql')
    );
    
    $wpdb->insert($table_name, $payment_data);
    
    return $wpdb->insert_id;
}

/**
 * Create payments table
 */
function iptv_pro_create_payments_table() {
    global $wpdb;
    
    $table_name = $wpdb->prefix . 'iptv_payments';
    
    $charset_collate = $wpdb->get_charset_collate();
    
    $sql = "CREATE TABLE IF NOT EXISTS $table_name (
        id mediumint(9) NOT NULL AUTO_INCREMENT,
        user_id bigint(20) NOT NULL,
        package_id bigint(20) NOT NULL,
        amount decimal(10,2) NOT NULL,
        payment_method varchar(50) NOT NULL,
        status varchar(20) NOT NULL DEFAULT 'pending',
        transaction_id varchar(255),
        created_at datetime DEFAULT CURRENT_TIMESTAMP,
        updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        KEY user_id (user_id),
        KEY package_id (package_id),
        KEY status (status)
    ) $charset_collate;";
    
    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    dbDelta($sql);
}

/**
 * Handle payment success
 */
function iptv_pro_handle_payment_success() {
    if (isset($_GET['payment_success']) && $_GET['payment_success'] == '1') {
        $package_id = intval($_GET['package_id']);
        $user_id = intval($_GET['user_id']);
        
        if ($package_id && $user_id) {
            // Activate subscription
            iptv_pro_activate_subscription($user_id, $package_id);
            
            // Update payment status
            global $wpdb;
            $table_name = $wpdb->prefix . 'iptv_payments';
            $wpdb->update(
                $table_name,
                array('status' => 'completed'),
                array('user_id' => $user_id, 'package_id' => $package_id, 'status' => 'pending'),
                array('%s'),
                array('%d', '%d', '%s')
            );
            
            // Send confirmation email
            iptv_pro_send_payment_confirmation_email($user_id, $package_id);
        }
    }
}
add_action('init', 'iptv_pro_handle_payment_success');

/**
 * Activate subscription
 */
function iptv_pro_activate_subscription($user_id, $package_id) {
    $duration = get_post_meta($package_id, '_iptv_duration', true);
    $end_date = date('Y-m-d', strtotime('+' . $duration . ' months'));
    
    // Update user meta
    update_user_meta($user_id, 'subscription_status', 'active');
    update_user_meta($user_id, 'subscription_end_date', $end_date);
    update_user_meta($user_id, 'package_type', $package_id);
    
    // Generate IPTV credentials if not exists
    if (!get_user_meta($user_id, 'iptv_username', true)) {
        iptv_pro_generate_iptv_credentials($user_id);
    }
    
    // Create subscription record
    $subscription_data = array(
        'post_title' => 'Subscription for User ' . $user_id,
        'post_type' => 'iptv_subscription',
        'post_status' => 'publish',
        'meta_input' => array(
            '_customer_id' => $user_id,
            '_package_id' => $package_id,
            '_start_date' => date('Y-m-d'),
            '_end_date' => $end_date,
            '_subscription_status' => 'active'
        )
    );
    
    wp_insert_post($subscription_data);
}

/**
 * Send payment confirmation email
 */
function iptv_pro_send_payment_confirmation_email($user_id, $package_id) {
    $user = get_userdata($user_id);
    $package = get_post($package_id);
    $subscription_info = iptv_pro_get_user_subscription_info($user_id);
    
    $subject = sprintf(__('Payment Confirmed - %s', 'iptv-pro'), get_bloginfo('name'));
    
    $message = sprintf(
        __('Hello %s,

Your payment has been successfully processed!

Package: %s
IPTV Username: %s
IPTV Password: %s
Server URL: %s
Subscription expires: %s

You can now access your IPTV service and manage your subscription from your dashboard:
%s

Thank you for choosing %s!

Best regards,
%s Team', 'iptv-pro'),
        $user->display_name,
        $package->post_title,
        $subscription_info['iptv_username'],
        $subscription_info['iptv_password'],
        $subscription_info['server_url'],
        date('F j, Y', strtotime($subscription_info['end_date'])),
        get_permalink(get_page_by_path('dashboard')),
        get_bloginfo('name'),
        get_bloginfo('name')
    );
    
    wp_mail($user->user_email, $subject, $message);
}

/**
 * Renew subscription AJAX handler
 */
function iptv_pro_renew_subscription() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'iptv_nonce')) {
        wp_die(__('Security check failed', 'iptv-pro'));
    }

    $subscription_id = intval($_POST['subscription_id']);
    $user_id = get_current_user_id();

    if (!$user_id || $subscription_id !== $user_id) {
        wp_send_json_error(__('Unauthorized', 'iptv-pro'));
    }

    // Get current package
    $package_id = get_user_meta($user_id, 'package_type', true);
    if (!$package_id) {
        wp_send_json_error(__('No active package found', 'iptv-pro'));
    }

    // Redirect to checkout with current package
    $checkout_url = add_query_arg('package', $package_id, get_permalink(get_page_by_path('checkout')));
    
    wp_send_json_success(array(
        'redirect_url' => $checkout_url
    ));
}
add_action('wp_ajax_iptv_renew_subscription', 'iptv_pro_renew_subscription');

/**
 * Cancel subscription AJAX handler
 */
function iptv_pro_cancel_subscription() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'iptv_nonce')) {
        wp_die(__('Security check failed', 'iptv-pro'));
    }

    $subscription_id = intval($_POST['subscription_id']);
    $user_id = get_current_user_id();

    if (!$user_id || $subscription_id !== $user_id) {
        wp_send_json_error(__('Unauthorized', 'iptv-pro'));
    }

    // Update subscription status
    update_user_meta($user_id, 'subscription_status', 'cancelled');
    
    // Update subscription post
    $subscriptions = get_posts(array(
        'post_type' => 'iptv_subscription',
        'meta_query' => array(
            array(
                'key' => '_customer_id',
                'value' => $user_id
            ),
            array(
                'key' => '_subscription_status',
                'value' => 'active'
            )
        )
    ));
    
    foreach ($subscriptions as $subscription) {
        update_post_meta($subscription->ID, '_subscription_status', 'cancelled');
    }
    
    wp_send_json_success(__('Subscription cancelled successfully', 'iptv-pro'));
}
add_action('wp_ajax_iptv_cancel_subscription', 'iptv_pro_cancel_subscription');

/**
 * Get user payment history
 */
function iptv_pro_get_user_payment_history($user_id) {
    global $wpdb;
    
    $table_name = $wpdb->prefix . 'iptv_payments';
    
    $payments = $wpdb->get_results($wpdb->prepare(
        "SELECT * FROM $table_name WHERE user_id = %d ORDER BY created_at DESC",
        $user_id
    ));
    
    return $payments;
}

/**
 * Admin payment management
 */
function iptv_pro_add_payment_admin_menu() {
    add_submenu_page(
        'edit.php?post_type=iptv_package',
        __('Payments', 'iptv-pro'),
        __('Payments', 'iptv-pro'),
        'manage_options',
        'iptv-payments',
        'iptv_pro_payment_admin_page'
    );
}
add_action('admin_menu', 'iptv_pro_add_payment_admin_menu');

/**
 * Payment admin page
 */
function iptv_pro_payment_admin_page() {
    global $wpdb;
    
    $table_name = $wpdb->prefix . 'iptv_payments';
    $payments = $wpdb->get_results("SELECT * FROM $table_name ORDER BY created_at DESC LIMIT 50");
    
    ?>
    <div class="wrap">
        <h1><?php _e('Payment Management', 'iptv-pro'); ?></h1>
        
        <table class="wp-list-table widefat fixed striped">
            <thead>
                <tr>
                    <th><?php _e('ID', 'iptv-pro'); ?></th>
                    <th><?php _e('User', 'iptv-pro'); ?></th>
                    <th><?php _e('Package', 'iptv-pro'); ?></th>
                    <th><?php _e('Amount', 'iptv-pro'); ?></th>
                    <th><?php _e('Method', 'iptv-pro'); ?></th>
                    <th><?php _e('Status', 'iptv-pro'); ?></th>
                    <th><?php _e('Date', 'iptv-pro'); ?></th>
                    <th><?php _e('Actions', 'iptv-pro'); ?></th>
                </tr>
            </thead>
            <tbody>
                <?php if ($payments) : ?>
                    <?php foreach ($payments as $payment) : ?>
                        <tr>
                            <td><?php echo $payment->id; ?></td>
                            <td>
                                <?php 
                                $user = get_userdata($payment->user_id);
                                echo $user ? $user->display_name : 'Unknown';
                                ?>
                            </td>
                            <td><?php echo get_the_title($payment->package_id); ?></td>
                            <td>$<?php echo number_format($payment->amount, 2); ?></td>
                            <td><?php echo ucfirst($payment->payment_method); ?></td>
                            <td>
                                <span class="payment-status payment-status-<?php echo $payment->status; ?>">
                                    <?php echo ucfirst($payment->status); ?>
                                </span>
                            </td>
                            <td><?php echo date('M j, Y H:i', strtotime($payment->created_at)); ?></td>
                            <td>
                                <?php if ($payment->status === 'pending') : ?>
                                    <button class="button button-primary approve-payment" data-payment-id="<?php echo $payment->id; ?>">
                                        <?php _e('Approve', 'iptv-pro'); ?>
                                    </button>
                                <?php endif; ?>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                <?php else : ?>
                    <tr>
                        <td colspan="8"><?php _e('No payments found.', 'iptv-pro'); ?></td>
                    </tr>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
    
    <style>
    .payment-status {
        padding: 4px 8px;
        border-radius: 4px;
        font-weight: bold;
        color: white;
    }
    .payment-status-completed { background: #10b981; }
    .payment-status-pending { background: #f59e0b; }
    .payment-status-failed { background: #ef4444; }
    .payment-status-cancelled { background: #6b7280; }
    </style>
    <?php
}

// Initialize payment table on theme activation
function iptv_pro_activate_theme() {
    iptv_pro_create_payments_table();
}
add_action('after_switch_theme', 'iptv_pro_activate_theme');

?>
