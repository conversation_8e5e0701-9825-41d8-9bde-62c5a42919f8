<?php
/**
 * Template Name: Internet Speed Test
 *
 * @package IPTV_Pro
 */

get_header(); ?>

<main class="main-content speed-test-page">
    <div class="container" style="padding: 2rem 0;">
        
        <!-- Speed Test Header -->
        <div style="text-align: center; margin-bottom: 3rem;">
            <h1 style="font-size: 3rem; font-weight: 700; margin-bottom: 1rem; color: #1f2937;">
                <?php _e('Internet Speed Test', 'iptv-pro'); ?>
            </h1>
            <p style="font-size: 1.25rem; color: #6b7280; max-width: 600px; margin: 0 auto;">
                <?php _e('Test your internet speed to ensure optimal IPTV streaming quality. We recommend at least 10 Mbps for HD and 25 Mbps for 4K content.', 'iptv-pro'); ?>
            </p>
        </div>

        <!-- Speed Test Widget -->
        <div class="speed-test-widget" style="max-width: 600px; margin: 0 auto 3rem auto;">
            <div id="speed-test-container">
                <!-- Initial State -->
                <div id="speed-test-initial" class="test-state">
                    <div style="font-size: 4rem; margin-bottom: 1rem;">⚡</div>
                    <h2 style="font-size: 2rem; font-weight: 600; margin-bottom: 1rem;">
                        <?php _e('Test Your Connection', 'iptv-pro'); ?>
                    </h2>
                    <p style="margin-bottom: 2rem; opacity: 0.9;">
                        <?php _e('Click the button below to start testing your internet speed', 'iptv-pro'); ?>
                    </p>
                    <button id="start-speed-test" class="btn btn-primary" style="background: white; color: #3b82f6; font-size: 1.25rem; padding: 1rem 2rem;">
                        <?php _e('Start Speed Test', 'iptv-pro'); ?>
                    </button>
                </div>

                <!-- Testing State -->
                <div id="speed-test-running" class="test-state" style="display: none;">
                    <div class="speed-gauge" style="position: relative; width: 200px; height: 200px; margin: 0 auto 2rem auto;">
                        <svg width="200" height="200" style="transform: rotate(-90deg);">
                            <circle cx="100" cy="100" r="80" fill="none" stroke="rgba(255,255,255,0.3)" stroke-width="8"/>
                            <circle id="progress-circle" cx="100" cy="100" r="80" fill="none" stroke="white" stroke-width="8" 
                                    stroke-dasharray="502.4" stroke-dashoffset="502.4" style="transition: stroke-dashoffset 0.3s;"/>
                        </svg>
                        <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); text-align: center;">
                            <div id="current-speed" style="font-size: 2rem; font-weight: 700;">0</div>
                            <div style="font-size: 0.875rem; opacity: 0.8;">Mbps</div>
                        </div>
                    </div>
                    <h3 id="test-phase" style="font-size: 1.25rem; font-weight: 600; margin-bottom: 1rem;">
                        <?php _e('Testing Download Speed...', 'iptv-pro'); ?>
                    </h3>
                    <div class="progress-bar" style="width: 100%; height: 8px; background: rgba(255,255,255,0.3); border-radius: 4px; overflow: hidden;">
                        <div id="test-progress" style="width: 0%; height: 100%; background: white; transition: width 0.3s;"></div>
                    </div>
                </div>

                <!-- Results State -->
                <div id="speed-test-results" class="test-state" style="display: none;">
                    <h2 style="font-size: 2rem; font-weight: 600; margin-bottom: 2rem;">
                        <?php _e('Speed Test Results', 'iptv-pro'); ?>
                    </h2>
                    
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 1.5rem; margin-bottom: 2rem;">
                        <div style="text-align: center;">
                            <div style="font-size: 2.5rem; font-weight: 700; margin-bottom: 0.5rem;" id="download-result">--</div>
                            <div style="opacity: 0.8;"><?php _e('Download (Mbps)', 'iptv-pro'); ?></div>
                        </div>
                        <div style="text-align: center;">
                            <div style="font-size: 2.5rem; font-weight: 700; margin-bottom: 0.5rem;" id="upload-result">--</div>
                            <div style="opacity: 0.8;"><?php _e('Upload (Mbps)', 'iptv-pro'); ?></div>
                        </div>
                        <div style="text-align: center;">
                            <div style="font-size: 2.5rem; font-weight: 700; margin-bottom: 0.5rem;" id="ping-result">--</div>
                            <div style="opacity: 0.8;"><?php _e('Ping (ms)', 'iptv-pro'); ?></div>
                        </div>
                    </div>
                    
                    <div id="speed-recommendation" style="background: rgba(255,255,255,0.2); padding: 1.5rem; border-radius: 8px; margin-bottom: 2rem;">
                        <!-- Recommendation will be inserted here -->
                    </div>
                    
                    <button id="test-again" class="btn btn-secondary" style="margin-right: 1rem;">
                        <?php _e('Test Again', 'iptv-pro'); ?>
                    </button>
                    <button id="view-packages" class="btn btn-primary" style="background: white; color: #3b82f6;">
                        <?php _e('View IPTV Packages', 'iptv-pro'); ?>
                    </button>
                </div>
            </div>
        </div>

        <!-- Speed Requirements Guide -->
        <div style="background: white; padding: 2rem; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.05); margin-bottom: 3rem;">
            <h2 style="font-size: 1.5rem; font-weight: 600; margin-bottom: 2rem; text-align: center; color: #1f2937;">
                <?php _e('IPTV Speed Requirements', 'iptv-pro'); ?>
            </h2>
            
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 2rem;">
                <div style="text-align: center; padding: 1.5rem; border: 1px solid #e5e7eb; border-radius: 8px;">
                    <div style="font-size: 2.5rem; margin-bottom: 1rem;">📺</div>
                    <h3 style="font-size: 1.125rem; font-weight: 600; margin-bottom: 1rem; color: #1f2937;">
                        <?php _e('Standard Definition', 'iptv-pro'); ?>
                    </h3>
                    <div style="font-size: 1.5rem; font-weight: 700; color: #3b82f6; margin-bottom: 0.5rem;">3-5 Mbps</div>
                    <p style="color: #6b7280; font-size: 0.875rem;">
                        <?php _e('Basic quality streaming for single device', 'iptv-pro'); ?>
                    </p>
                </div>
                
                <div style="text-align: center; padding: 1.5rem; border: 1px solid #e5e7eb; border-radius: 8px;">
                    <div style="font-size: 2.5rem; margin-bottom: 1rem;">🎬</div>
                    <h3 style="font-size: 1.125rem; font-weight: 600; margin-bottom: 1rem; color: #1f2937;">
                        <?php _e('High Definition', 'iptv-pro'); ?>
                    </h3>
                    <div style="font-size: 1.5rem; font-weight: 700; color: #3b82f6; margin-bottom: 0.5rem;">10-15 Mbps</div>
                    <p style="color: #6b7280; font-size: 0.875rem;">
                        <?php _e('HD quality streaming, recommended minimum', 'iptv-pro'); ?>
                    </p>
                </div>
                
                <div style="text-align: center; padding: 1.5rem; border: 1px solid #e5e7eb; border-radius: 8px;">
                    <div style="font-size: 2.5rem; margin-bottom: 1rem;">✨</div>
                    <h3 style="font-size: 1.125rem; font-weight: 600; margin-bottom: 1rem; color: #1f2937;">
                        <?php _e('4K Ultra HD', 'iptv-pro'); ?>
                    </h3>
                    <div style="font-size: 1.5rem; font-weight: 700; color: #3b82f6; margin-bottom: 0.5rem;">25+ Mbps</div>
                    <p style="color: #6b7280; font-size: 0.875rem;">
                        <?php _e('Premium 4K streaming experience', 'iptv-pro'); ?>
                    </p>
                </div>
                
                <div style="text-align: center; padding: 1.5rem; border: 1px solid #e5e7eb; border-radius: 8px;">
                    <div style="font-size: 2.5rem; margin-bottom: 1rem;">👨‍👩‍👧‍👦</div>
                    <h3 style="font-size: 1.125rem; font-weight: 600; margin-bottom: 1rem; color: #1f2937;">
                        <?php _e('Multiple Devices', 'iptv-pro'); ?>
                    </h3>
                    <div style="font-size: 1.5rem; font-weight: 700; color: #3b82f6; margin-bottom: 0.5rem;">50+ Mbps</div>
                    <p style="color: #6b7280; font-size: 0.875rem;">
                        <?php _e('For families with multiple simultaneous streams', 'iptv-pro'); ?>
                    </p>
                </div>
            </div>
        </div>

        <!-- Optimization Tips -->
        <div style="background: white; padding: 2rem; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.05);">
            <h2 style="font-size: 1.5rem; font-weight: 600; margin-bottom: 2rem; text-align: center; color: #1f2937;">
                <?php _e('Optimize Your IPTV Experience', 'iptv-pro'); ?>
            </h2>
            
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem;">
                <div>
                    <h3 style="font-size: 1.125rem; font-weight: 600; margin-bottom: 1rem; color: #374151;">
                        🌐 <?php _e('Network Optimization', 'iptv-pro'); ?>
                    </h3>
                    <ul style="color: #6b7280; line-height: 1.6;">
                        <li>• <?php _e('Use wired connection when possible', 'iptv-pro'); ?></li>
                        <li>• <?php _e('Position router centrally', 'iptv-pro'); ?></li>
                        <li>• <?php _e('Update router firmware regularly', 'iptv-pro'); ?></li>
                        <li>• <?php _e('Use 5GHz WiFi band for streaming', 'iptv-pro'); ?></li>
                    </ul>
                </div>
                
                <div>
                    <h3 style="font-size: 1.125rem; font-weight: 600; margin-bottom: 1rem; color: #374151;">
                        📱 <?php _e('Device Settings', 'iptv-pro'); ?>
                    </h3>
                    <ul style="color: #6b7280; line-height: 1.6;">
                        <li>• <?php _e('Close unnecessary apps', 'iptv-pro'); ?></li>
                        <li>• <?php _e('Clear app cache regularly', 'iptv-pro'); ?></li>
                        <li>• <?php _e('Use recommended IPTV apps', 'iptv-pro'); ?></li>
                        <li>• <?php _e('Adjust video quality if needed', 'iptv-pro'); ?></li>
                    </ul>
                </div>
                
                <div>
                    <h3 style="font-size: 1.125rem; font-weight: 600; margin-bottom: 1rem; color: #374151;">
                        ⚡ <?php _e('Troubleshooting', 'iptv-pro'); ?>
                    </h3>
                    <ul style="color: #6b7280; line-height: 1.6;">
                        <li>• <?php _e('Restart router if experiencing issues', 'iptv-pro'); ?></li>
                        <li>• <?php _e('Check for ISP throttling', 'iptv-pro'); ?></li>
                        <li>• <?php _e('Contact support for server issues', 'iptv-pro'); ?></li>
                        <li>• <?php _e('Test different servers if available', 'iptv-pro'); ?></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</main>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const startButton = document.getElementById('start-speed-test');
    const testAgainButton = document.getElementById('test-again');
    const viewPackagesButton = document.getElementById('view-packages');
    
    let testResults = {
        download: 0,
        upload: 0,
        ping: 0
    };
    
    startButton.addEventListener('click', startSpeedTest);
    testAgainButton.addEventListener('click', resetTest);
    viewPackagesButton.addEventListener('click', function() {
        window.location.href = '<?php echo get_permalink(get_page_by_path('packages')); ?>';
    });
    
    function startSpeedTest() {
        showTestState('running');
        
        // Simulate speed test (in real implementation, use actual speed test API)
        simulateSpeedTest();
    }
    
    function simulateSpeedTest() {
        let progress = 0;
        let phase = 'download';
        let currentSpeed = 0;
        
        const testInterval = setInterval(() => {
            progress += 2;
            
            // Simulate varying speeds
            if (phase === 'download') {
                currentSpeed = Math.random() * 50 + 10;
                document.getElementById('test-phase').textContent = '<?php _e('Testing Download Speed...', 'iptv-pro'); ?>';
            } else if (phase === 'upload') {
                currentSpeed = Math.random() * 20 + 5;
                document.getElementById('test-phase').textContent = '<?php _e('Testing Upload Speed...', 'iptv-pro'); ?>';
            } else {
                currentSpeed = Math.random() * 50 + 20;
                document.getElementById('test-phase').textContent = '<?php _e('Testing Ping...', 'iptv-pro'); ?>';
            }
            
            // Update UI
            document.getElementById('current-speed').textContent = currentSpeed.toFixed(1);
            document.getElementById('test-progress').style.width = progress + '%';
            
            // Update circular progress
            const circumference = 2 * Math.PI * 80;
            const offset = circumference - (progress / 100) * circumference;
            document.getElementById('progress-circle').style.strokeDashoffset = offset;
            
            // Phase transitions
            if (progress >= 33 && phase === 'download') {
                testResults.download = currentSpeed;
                phase = 'upload';
            } else if (progress >= 66 && phase === 'upload') {
                testResults.upload = currentSpeed;
                phase = 'ping';
            } else if (progress >= 100) {
                testResults.ping = currentSpeed;
                clearInterval(testInterval);
                showResults();
            }
        }, 100);
    }
    
    function showResults() {
        // Display results
        document.getElementById('download-result').textContent = testResults.download.toFixed(1);
        document.getElementById('upload-result').textContent = testResults.upload.toFixed(1);
        document.getElementById('ping-result').textContent = testResults.ping.toFixed(0);
        
        // Generate recommendation
        generateRecommendation();
        
        showTestState('results');
    }
    
    function generateRecommendation() {
        const download = testResults.download;
        let recommendation = '';
        let icon = '';
        let color = '';
        
        if (download >= 25) {
            icon = '🎉';
            color = '#10b981';
            recommendation = '<?php _e('Excellent! Your connection is perfect for 4K IPTV streaming. You can enjoy our premium packages with multiple simultaneous streams.', 'iptv-pro'); ?>';
        } else if (download >= 15) {
            icon = '👍';
            color = '#3b82f6';
            recommendation = '<?php _e('Great! Your connection supports HD streaming. Perfect for our standard and premium packages.', 'iptv-pro'); ?>';
        } else if (download >= 10) {
            icon = '✅';
            color = '#f59e0b';
            recommendation = '<?php _e('Good! Your connection meets the minimum requirements for HD IPTV. Consider our basic packages for optimal experience.', 'iptv-pro'); ?>';
        } else {
            icon = '⚠️';
            color = '#ef4444';
            recommendation = '<?php _e('Your connection may struggle with HD streaming. Consider upgrading your internet plan or optimizing your network for better IPTV experience.', 'iptv-pro'); ?>';
        }
        
        document.getElementById('speed-recommendation').innerHTML = `
            <div style="display: flex; align-items: center; gap: 1rem;">
                <div style="font-size: 2rem;">${icon}</div>
                <div style="color: ${color}; font-weight: 600;">${recommendation}</div>
            </div>
        `;
    }
    
    function showTestState(state) {
        document.querySelectorAll('.test-state').forEach(el => el.style.display = 'none');
        document.getElementById('speed-test-' + state).style.display = 'block';
    }
    
    function resetTest() {
        testResults = { download: 0, upload: 0, ping: 0 };
        document.getElementById('test-progress').style.width = '0%';
        document.getElementById('progress-circle').style.strokeDashoffset = '502.4';
        document.getElementById('current-speed').textContent = '0';
        showTestState('initial');
    }
});
</script>

<style>
@media (max-width: 768px) {
    .speed-test-page h1 {
        font-size: 2rem !important;
    }
    
    .speed-gauge {
        width: 150px !important;
        height: 150px !important;
    }
    
    .speed-gauge svg {
        width: 150px !important;
        height: 150px !important;
    }
}
</style>

<?php get_footer(); ?>
