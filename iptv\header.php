<?php
/**
 * The header for our theme
 *
 * @package IPTV_Pro
 */
?>
<!doctype html>
<html <?php language_attributes(); ?>>
<head>
    <meta charset="<?php bloginfo('charset'); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="profile" href="https://gmpg.org/xfn/11">
    
    <?php wp_head(); ?>
</head>

<body <?php body_class(); ?>>
<?php wp_body_open(); ?>

<div id="page" class="site">
    <header id="masthead" class="site-header">
        <div class="container">
            <div class="header-content">
                <div class="site-branding">
                    <?php
                    if (has_custom_logo()) {
                        the_custom_logo();
                    } else {
                        ?>
                        <a href="<?php echo esc_url(home_url('/')); ?>" class="logo" rel="home">
                            <?php bloginfo('name'); ?>
                        </a>
                        <?php
                    }
                    ?>
                </div>

                <nav id="site-navigation" class="main-nav">
                    <?php
                    wp_nav_menu(array(
                        'theme_location' => 'primary',
                        'menu_id'        => 'primary-menu',
                        'fallback_cb'    => 'iptv_pro_fallback_menu',
                    ));
                    ?>
                </nav>

                <div class="header-actions">
                    <?php if (is_user_logged_in()) : ?>
                        <a href="<?php echo esc_url(get_permalink(get_page_by_path('dashboard'))); ?>" class="btn btn-secondary">
                            <?php _e('Dashboard', 'iptv-pro'); ?>
                        </a>
                        <a href="<?php echo wp_logout_url(home_url()); ?>" class="btn btn-primary">
                            <?php _e('Logout', 'iptv-pro'); ?>
                        </a>
                    <?php else : ?>
                        <a href="<?php echo esc_url(get_permalink(get_page_by_path('login'))); ?>" class="btn btn-secondary">
                            <?php _e('Login', 'iptv-pro'); ?>
                        </a>
                        <a href="<?php echo esc_url(get_permalink(get_page_by_path('register'))); ?>" class="btn btn-primary">
                            <?php _e('Sign Up', 'iptv-pro'); ?>
                        </a>
                    <?php endif; ?>
                </div>

                <!-- Mobile menu toggle -->
                <button class="mobile-menu-toggle" id="mobile-menu-toggle" style="display: none; background: none; border: none; color: white; font-size: 1.5rem; cursor: pointer;">
                    ☰
                </button>
            </div>
        </div>
    </header>

    <!-- Mobile Navigation -->
    <nav id="mobile-navigation" class="mobile-nav" style="display: none; background: #1f2937; position: fixed; top: 80px; left: 0; right: 0; z-index: 999; padding: 1rem;">
        <div class="container">
            <?php
            wp_nav_menu(array(
                'theme_location' => 'primary',
                'menu_id'        => 'mobile-menu',
                'fallback_cb'    => 'iptv_pro_fallback_menu',
                'container'      => false,
                'menu_class'     => 'mobile-menu-list',
            ));
            ?>
            <div class="mobile-header-actions" style="margin-top: 1rem; display: flex; gap: 1rem; flex-direction: column;">
                <?php if (is_user_logged_in()) : ?>
                    <a href="<?php echo esc_url(get_permalink(get_page_by_path('dashboard'))); ?>" class="btn btn-secondary">
                        <?php _e('Dashboard', 'iptv-pro'); ?>
                    </a>
                    <a href="<?php echo wp_logout_url(home_url()); ?>" class="btn btn-primary">
                        <?php _e('Logout', 'iptv-pro'); ?>
                    </a>
                <?php else : ?>
                    <a href="<?php echo esc_url(get_permalink(get_page_by_path('login'))); ?>" class="btn btn-secondary">
                        <?php _e('Login', 'iptv-pro'); ?>
                    </a>
                    <a href="<?php echo esc_url(get_permalink(get_page_by_path('register'))); ?>" class="btn btn-primary">
                        <?php _e('Sign Up', 'iptv-pro'); ?>
                    </a>
                <?php endif; ?>
            </div>
        </div>
    </nav>

<?php
// Fallback menu function
function iptv_pro_fallback_menu() {
    echo '<ul id="primary-menu" class="menu">';
    echo '<li><a href="' . home_url('/') . '">' . __('Home', 'iptv-pro') . '</a></li>';
    echo '<li><a href="' . get_permalink(get_page_by_path('packages')) . '">' . __('Packages', 'iptv-pro') . '</a></li>';
    echo '<li><a href="' . get_permalink(get_page_by_path('channels')) . '">' . __('Channels', 'iptv-pro') . '</a></li>';
    echo '<li><a href="' . get_permalink(get_page_by_path('support')) . '">' . __('Support', 'iptv-pro') . '</a></li>';
    echo '<li><a href="' . get_permalink(get_page_by_path('contact')) . '">' . __('Contact', 'iptv-pro') . '</a></li>';
    echo '</ul>';
}
?>
