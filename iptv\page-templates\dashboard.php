<?php
/**
 * Template Name: Customer Dashboard
 *
 * @package IPTV_Pro
 */

// Redirect if not logged in
if (!is_user_logged_in()) {
    wp_redirect(wp_login_url(get_permalink()));
    exit;
}

get_header();

$current_user = wp_get_current_user();
$subscription_info = iptv_pro_get_user_subscription_info();
?>

<main class="main-content dashboard-page">
    <div class="container" style="padding: 2rem 0;">
        
        <!-- Dashboard Header -->
        <div class="dashboard-header" style="background: white; padding: 2rem; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.05); margin-bottom: 2rem;">
            <div style="display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; gap: 1rem;">
                <div>
                    <h1 style="font-size: 2rem; font-weight: 700; margin-bottom: 0.5rem; color: #1f2937;">
                        <?php printf(__('Welcome back, %s!', 'iptv-pro'), $current_user->display_name); ?>
                    </h1>
                    <p style="color: #6b7280;">
                        <?php printf(__('Customer ID: %s', 'iptv-pro'), $subscription_info['customer_id']); ?>
                    </p>
                </div>
                <div class="subscription-status">
                    <?php
                    $status = $subscription_info['status'];
                    $status_colors = array(
                        'active' => '#10b981',
                        'expired' => '#ef4444',
                        'cancelled' => '#6b7280',
                        'suspended' => '#f59e0b',
                        'inactive' => '#9ca3af'
                    );
                    $color = $status_colors[$status] ?? '#6b7280';
                    ?>
                    <span style="background: <?php echo $color; ?>; color: white; padding: 0.5rem 1rem; border-radius: 20px; font-weight: 600; font-size: 0.875rem;">
                        <?php echo ucfirst($status); ?>
                    </span>
                </div>
            </div>
        </div>

        <!-- Dashboard Navigation -->
        <div class="dashboard-tabs" style="background: white; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.05); margin-bottom: 2rem; overflow: hidden;">
            <div style="display: flex; border-bottom: 1px solid #e5e7eb; overflow-x: auto;">
                <button class="tab-link active" data-tab="overview" style="padding: 1rem 1.5rem; border: none; background: none; font-weight: 500; color: #3b82f6; border-bottom: 2px solid #3b82f6; cursor: pointer; white-space: nowrap;">
                    <?php _e('Overview', 'iptv-pro'); ?>
                </button>
                <button class="tab-link" data-tab="subscription" style="padding: 1rem 1.5rem; border: none; background: none; font-weight: 500; color: #6b7280; cursor: pointer; white-space: nowrap;">
                    <?php _e('Subscription', 'iptv-pro'); ?>
                </button>
                <button class="tab-link" data-tab="payments" style="padding: 1rem 1.5rem; border: none; background: none; font-weight: 500; color: #6b7280; cursor: pointer; white-space: nowrap;">
                    <?php _e('Payments', 'iptv-pro'); ?>
                </button>
                <button class="tab-link" data-tab="support" style="padding: 1rem 1.5rem; border: none; background: none; font-weight: 500; color: #6b7280; cursor: pointer; white-space: nowrap;">
                    <?php _e('Support', 'iptv-pro'); ?>
                </button>
                <button class="tab-link" data-tab="downloads" style="padding: 1rem 1.5rem; border: none; background: none; font-weight: 500; color: #6b7280; cursor: pointer; white-space: nowrap;">
                    <?php _e('Downloads', 'iptv-pro'); ?>
                </button>
                <button class="tab-link" data-tab="profile" style="padding: 1rem 1.5rem; border: none; background: none; font-weight: 500; color: #6b7280; cursor: pointer; white-space: nowrap;">
                    <?php _e('Profile', 'iptv-pro'); ?>
                </button>
            </div>
        </div>

        <!-- Tab Content -->
        <div class="tab-contents">
            
            <!-- Overview Tab -->
            <div id="overview" class="tab-content active">
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem; margin-bottom: 2rem;">
                    
                    <!-- Subscription Status Card -->
                    <div style="background: white; padding: 2rem; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.05);">
                        <h3 style="font-size: 1.25rem; font-weight: 600; margin-bottom: 1rem; color: #1f2937;">
                            <?php _e('Subscription Status', 'iptv-pro'); ?>
                        </h3>
                        <div style="margin-bottom: 1rem;">
                            <span style="background: <?php echo $color; ?>; color: white; padding: 0.5rem 1rem; border-radius: 8px; font-weight: 600;">
                                <?php echo ucfirst($status); ?>
                            </span>
                        </div>
                        <?php if ($subscription_info['end_date']) : ?>
                            <p style="color: #6b7280; margin-bottom: 1rem;">
                                <?php printf(__('Expires: %s', 'iptv-pro'), date('F j, Y', strtotime($subscription_info['end_date']))); ?>
                            </p>
                        <?php endif; ?>
                        <?php if ($status === 'active') : ?>
                            <a href="#" class="btn btn-primary renew-subscription" data-subscription-id="<?php echo $current_user->ID; ?>">
                                <?php _e('Renew Subscription', 'iptv-pro'); ?>
                            </a>
                        <?php elseif ($status === 'expired' || $status === 'inactive') : ?>
                            <a href="<?php echo get_permalink(get_page_by_path('packages')); ?>" class="btn btn-primary">
                                <?php _e('Reactivate', 'iptv-pro'); ?>
                            </a>
                        <?php endif; ?>
                    </div>

                    <!-- IPTV Credentials Card -->
                    <?php if ($subscription_info['iptv_username']) : ?>
                        <div style="background: white; padding: 2rem; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.05);">
                            <h3 style="font-size: 1.25rem; font-weight: 600; margin-bottom: 1rem; color: #1f2937;">
                                <?php _e('IPTV Credentials', 'iptv-pro'); ?>
                            </h3>
                            <div style="background: #f8fafc; padding: 1rem; border-radius: 8px; margin-bottom: 1rem;">
                                <p style="margin-bottom: 0.5rem;"><strong><?php _e('Username:', 'iptv-pro'); ?></strong> <?php echo esc_html($subscription_info['iptv_username']); ?></p>
                                <p style="margin-bottom: 0.5rem;"><strong><?php _e('Password:', 'iptv-pro'); ?></strong> 
                                    <span id="password-hidden">••••••••</span>
                                    <span id="password-visible" style="display: none;"><?php echo esc_html($subscription_info['iptv_password']); ?></span>
                                    <button id="toggle-password" style="background: none; border: none; color: #3b82f6; cursor: pointer; margin-left: 0.5rem;">
                                        <?php _e('Show', 'iptv-pro'); ?>
                                    </button>
                                </p>
                                <?php if ($subscription_info['server_url']) : ?>
                                    <p><strong><?php _e('Server:', 'iptv-pro'); ?></strong> <?php echo esc_html($subscription_info['server_url']); ?></p>
                                <?php endif; ?>
                            </div>
                            <button class="btn btn-secondary" onclick="copyCredentials()">
                                <?php _e('Copy Credentials', 'iptv-pro'); ?>
                            </button>
                        </div>
                    <?php endif; ?>

                    <!-- Quick Actions Card -->
                    <div style="background: white; padding: 2rem; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.05);">
                        <h3 style="font-size: 1.25rem; font-weight: 600; margin-bottom: 1rem; color: #1f2937;">
                            <?php _e('Quick Actions', 'iptv-pro'); ?>
                        </h3>
                        <div style="display: flex; flex-direction: column; gap: 0.75rem;">
                            <a href="<?php echo get_permalink(get_page_by_path('channels')); ?>" class="btn btn-secondary" style="text-align: center;">
                                <?php _e('View Channel List', 'iptv-pro'); ?>
                            </a>
                            <a href="<?php echo get_permalink(get_page_by_path('apps')); ?>" class="btn btn-secondary" style="text-align: center;">
                                <?php _e('Download Apps', 'iptv-pro'); ?>
                            </a>
                            <a href="<?php echo get_permalink(get_page_by_path('setup-guide')); ?>" class="btn btn-secondary" style="text-align: center;">
                                <?php _e('Setup Guide', 'iptv-pro'); ?>
                            </a>
                            <a href="<?php echo get_permalink(get_page_by_path('support')); ?>" class="btn btn-secondary" style="text-align: center;">
                                <?php _e('Get Support', 'iptv-pro'); ?>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Recent Activity -->
                <div style="background: white; padding: 2rem; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.05);">
                    <h3 style="font-size: 1.25rem; font-weight: 600; margin-bottom: 1rem; color: #1f2937;">
                        <?php _e('Recent Activity', 'iptv-pro'); ?>
                    </h3>
                    <div style="color: #6b7280;">
                        <p><?php _e('No recent activity to display.', 'iptv-pro'); ?></p>
                    </div>
                </div>
            </div>

            <!-- Subscription Tab -->
            <div id="subscription" class="tab-content" style="display: none;">
                <div style="background: white; padding: 2rem; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.05);">
                    <h3 style="font-size: 1.5rem; font-weight: 600; margin-bottom: 2rem; color: #1f2937;">
                        <?php _e('Subscription Details', 'iptv-pro'); ?>
                    </h3>
                    
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 2rem;">
                        <div>
                            <h4 style="font-weight: 600; margin-bottom: 0.5rem; color: #374151;"><?php _e('Current Package', 'iptv-pro'); ?></h4>
                            <p style="color: #6b7280;">
                                <?php 
                                if ($subscription_info['package_type']) {
                                    echo get_the_title($subscription_info['package_type']);
                                } else {
                                    _e('No active package', 'iptv-pro');
                                }
                                ?>
                            </p>
                        </div>
                        
                        <div>
                            <h4 style="font-weight: 600; margin-bottom: 0.5rem; color: #374151;"><?php _e('Status', 'iptv-pro'); ?></h4>
                            <p style="color: <?php echo $color; ?>; font-weight: 600;">
                                <?php echo ucfirst($status); ?>
                            </p>
                        </div>
                        
                        <?php if ($subscription_info['end_date']) : ?>
                            <div>
                                <h4 style="font-weight: 600; margin-bottom: 0.5rem; color: #374151;"><?php _e('Expiry Date', 'iptv-pro'); ?></h4>
                                <p style="color: #6b7280;">
                                    <?php echo date('F j, Y', strtotime($subscription_info['end_date'])); ?>
                                </p>
                            </div>
                        <?php endif; ?>
                        
                        <div>
                            <h4 style="font-weight: 600; margin-bottom: 0.5rem; color: #374151;"><?php _e('Max Connections', 'iptv-pro'); ?></h4>
                            <p style="color: #6b7280;">
                                <?php echo $subscription_info['max_connections'] ?: '1'; ?>
                            </p>
                        </div>
                    </div>
                    
                    <div style="margin-top: 2rem; padding-top: 2rem; border-top: 1px solid #e5e7eb;">
                        <h4 style="font-weight: 600; margin-bottom: 1rem; color: #374151;"><?php _e('Actions', 'iptv-pro'); ?></h4>
                        <div style="display: flex; gap: 1rem; flex-wrap: wrap;">
                            <?php if ($status === 'active') : ?>
                                <button class="btn btn-primary renew-subscription" data-subscription-id="<?php echo $current_user->ID; ?>">
                                    <?php _e('Renew Subscription', 'iptv-pro'); ?>
                                </button>
                                <button class="btn btn-secondary cancel-subscription" data-subscription-id="<?php echo $current_user->ID; ?>">
                                    <?php _e('Cancel Subscription', 'iptv-pro'); ?>
                                </button>
                            <?php else : ?>
                                <a href="<?php echo get_permalink(get_page_by_path('packages')); ?>" class="btn btn-primary">
                                    <?php _e('Subscribe Now', 'iptv-pro'); ?>
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Payments Tab -->
            <div id="payments" class="tab-content" style="display: none;">
                <div style="background: white; padding: 2rem; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.05);">
                    <h3 style="font-size: 1.5rem; font-weight: 600; margin-bottom: 2rem; color: #1f2937;">
                        <?php _e('Payment History', 'iptv-pro'); ?>
                    </h3>
                    
                    <div style="overflow-x: auto;">
                        <table style="width: 100%; border-collapse: collapse;">
                            <thead>
                                <tr style="border-bottom: 2px solid #e5e7eb;">
                                    <th style="text-align: left; padding: 1rem; font-weight: 600; color: #374151;"><?php _e('Date', 'iptv-pro'); ?></th>
                                    <th style="text-align: left; padding: 1rem; font-weight: 600; color: #374151;"><?php _e('Amount', 'iptv-pro'); ?></th>
                                    <th style="text-align: left; padding: 1rem; font-weight: 600; color: #374151;"><?php _e('Method', 'iptv-pro'); ?></th>
                                    <th style="text-align: left; padding: 1rem; font-weight: 600; color: #374151;"><?php _e('Status', 'iptv-pro'); ?></th>
                                    <th style="text-align: left; padding: 1rem; font-weight: 600; color: #374151;"><?php _e('Invoice', 'iptv-pro'); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td colspan="5" style="text-align: center; padding: 2rem; color: #6b7280;">
                                        <?php _e('No payment history available.', 'iptv-pro'); ?>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Support Tab -->
            <div id="support" class="tab-content" style="display: none;">
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem;">
                    
                    <!-- Submit Ticket -->
                    <div style="background: white; padding: 2rem; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.05);">
                        <h3 style="font-size: 1.25rem; font-weight: 600; margin-bottom: 1rem; color: #1f2937;">
                            <?php _e('Submit Support Ticket', 'iptv-pro'); ?>
                        </h3>
                        <form class="support-form">
                            <div style="margin-bottom: 1rem;">
                                <label style="display: block; font-weight: 500; margin-bottom: 0.5rem; color: #374151;">
                                    <?php _e('Subject', 'iptv-pro'); ?>
                                </label>
                                <input type="text" name="subject" required style="width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 6px;">
                            </div>
                            <div style="margin-bottom: 1rem;">
                                <label style="display: block; font-weight: 500; margin-bottom: 0.5rem; color: #374151;">
                                    <?php _e('Priority', 'iptv-pro'); ?>
                                </label>
                                <select name="priority" style="width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 6px;">
                                    <option value="low"><?php _e('Low', 'iptv-pro'); ?></option>
                                    <option value="medium" selected><?php _e('Medium', 'iptv-pro'); ?></option>
                                    <option value="high"><?php _e('High', 'iptv-pro'); ?></option>
                                    <option value="urgent"><?php _e('Urgent', 'iptv-pro'); ?></option>
                                </select>
                            </div>
                            <div style="margin-bottom: 1rem;">
                                <label style="display: block; font-weight: 500; margin-bottom: 0.5rem; color: #374151;">
                                    <?php _e('Message', 'iptv-pro'); ?>
                                </label>
                                <textarea name="message" rows="4" required style="width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 6px; resize: vertical;"></textarea>
                            </div>
                            <button type="submit" class="btn btn-primary submit-btn">
                                <?php _e('Submit Ticket', 'iptv-pro'); ?>
                            </button>
                        </form>
                    </div>

                    <!-- Support Options -->
                    <div style="background: white; padding: 2rem; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.05);">
                        <h3 style="font-size: 1.25rem; font-weight: 600; margin-bottom: 1rem; color: #1f2937;">
                            <?php _e('Support Options', 'iptv-pro'); ?>
                        </h3>
                        <div style="display: flex; flex-direction: column; gap: 1rem;">
                            <a href="<?php echo get_permalink(get_page_by_path('faq')); ?>" class="btn btn-secondary" style="text-align: center;">
                                <?php _e('FAQ', 'iptv-pro'); ?>
                            </a>
                            <a href="<?php echo get_permalink(get_page_by_path('setup-guide')); ?>" class="btn btn-secondary" style="text-align: center;">
                                <?php _e('Setup Guide', 'iptv-pro'); ?>
                            </a>
                            <?php if (get_theme_mod('whatsapp_number')) : ?>
                                <a href="https://wa.me/<?php echo esc_attr(str_replace('+', '', get_theme_mod('whatsapp_number'))); ?>" target="_blank" class="btn btn-secondary" style="text-align: center; background: #25d366; color: white;">
                                    <?php _e('WhatsApp Support', 'iptv-pro'); ?>
                                </a>
                            <?php endif; ?>
                            <a href="<?php echo get_permalink(get_page_by_path('contact')); ?>" class="btn btn-secondary" style="text-align: center;">
                                <?php _e('Contact Us', 'iptv-pro'); ?>
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Downloads Tab -->
            <div id="downloads" class="tab-content" style="display: none;">
                <div style="background: white; padding: 2rem; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.05);">
                    <h3 style="font-size: 1.5rem; font-weight: 600; margin-bottom: 2rem; color: #1f2937;">
                        <?php _e('Download IPTV Apps', 'iptv-pro'); ?>
                    </h3>
                    
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 2rem;">
                        <!-- Android Apps -->
                        <div style="text-align: center; padding: 1.5rem; border: 1px solid #e5e7eb; border-radius: 8px;">
                            <div style="font-size: 3rem; margin-bottom: 1rem;">📱</div>
                            <h4 style="font-weight: 600; margin-bottom: 1rem; color: #1f2937;"><?php _e('Android', 'iptv-pro'); ?></h4>
                            <p style="color: #6b7280; margin-bottom: 1rem; font-size: 0.875rem;">
                                <?php _e('IPTV Smarters Pro, TiviMate, Perfect Player', 'iptv-pro'); ?>
                            </p>
                            <a href="#" class="btn btn-primary" style="width: 100%;">
                                <?php _e('Download', 'iptv-pro'); ?>
                            </a>
                        </div>

                        <!-- iOS Apps -->
                        <div style="text-align: center; padding: 1.5rem; border: 1px solid #e5e7eb; border-radius: 8px;">
                            <div style="font-size: 3rem; margin-bottom: 1rem;">📱</div>
                            <h4 style="font-weight: 600; margin-bottom: 1rem; color: #1f2937;"><?php _e('iOS', 'iptv-pro'); ?></h4>
                            <p style="color: #6b7280; margin-bottom: 1rem; font-size: 0.875rem;">
                                <?php _e('IPTV Smarters Pro, GSE Smart IPTV', 'iptv-pro'); ?>
                            </p>
                            <a href="#" class="btn btn-primary" style="width: 100%;">
                                <?php _e('Download', 'iptv-pro'); ?>
                            </a>
                        </div>

                        <!-- Windows Apps -->
                        <div style="text-align: center; padding: 1.5rem; border: 1px solid #e5e7eb; border-radius: 8px;">
                            <div style="font-size: 3rem; margin-bottom: 1rem;">💻</div>
                            <h4 style="font-weight: 600; margin-bottom: 1rem; color: #1f2937;"><?php _e('Windows', 'iptv-pro'); ?></h4>
                            <p style="color: #6b7280; margin-bottom: 1rem; font-size: 0.875rem;">
                                <?php _e('VLC Media Player, IPTV Smarters Pro', 'iptv-pro'); ?>
                            </p>
                            <a href="#" class="btn btn-primary" style="width: 100%;">
                                <?php _e('Download', 'iptv-pro'); ?>
                            </a>
                        </div>

                        <!-- Smart TV Apps -->
                        <div style="text-align: center; padding: 1.5rem; border: 1px solid #e5e7eb; border-radius: 8px;">
                            <div style="font-size: 3rem; margin-bottom: 1rem;">📺</div>
                            <h4 style="font-weight: 600; margin-bottom: 1rem; color: #1f2937;"><?php _e('Smart TV', 'iptv-pro'); ?></h4>
                            <p style="color: #6b7280; margin-bottom: 1rem; font-size: 0.875rem;">
                                <?php _e('Smart IPTV, SS IPTV, IPTV Smarters', 'iptv-pro'); ?>
                            </p>
                            <a href="#" class="btn btn-primary" style="width: 100%;">
                                <?php _e('Download', 'iptv-pro'); ?>
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Profile Tab -->
            <div id="profile" class="tab-content" style="display: none;">
                <div style="background: white; padding: 2rem; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.05);">
                    <h3 style="font-size: 1.5rem; font-weight: 600; margin-bottom: 2rem; color: #1f2937;">
                        <?php _e('Profile Information', 'iptv-pro'); ?>
                    </h3>
                    
                    <form class="profile-form">
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1.5rem;">
                            <div>
                                <label style="display: block; font-weight: 500; margin-bottom: 0.5rem; color: #374151;">
                                    <?php _e('First Name', 'iptv-pro'); ?>
                                </label>
                                <input type="text" name="first_name" value="<?php echo esc_attr($current_user->first_name); ?>" style="width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 6px;">
                            </div>
                            
                            <div>
                                <label style="display: block; font-weight: 500; margin-bottom: 0.5rem; color: #374151;">
                                    <?php _e('Last Name', 'iptv-pro'); ?>
                                </label>
                                <input type="text" name="last_name" value="<?php echo esc_attr($current_user->last_name); ?>" style="width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 6px;">
                            </div>
                            
                            <div>
                                <label style="display: block; font-weight: 500; margin-bottom: 0.5rem; color: #374151;">
                                    <?php _e('Email', 'iptv-pro'); ?>
                                </label>
                                <input type="email" name="email" value="<?php echo esc_attr($current_user->user_email); ?>" style="width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 6px;">
                            </div>
                            
                            <div>
                                <label style="display: block; font-weight: 500; margin-bottom: 0.5rem; color: #374151;">
                                    <?php _e('Phone', 'iptv-pro'); ?>
                                </label>
                                <input type="tel" name="phone" value="<?php echo esc_attr(get_user_meta($current_user->ID, 'phone', true)); ?>" style="width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 6px;">
                            </div>
                        </div>
                        
                        <div style="margin-top: 2rem;">
                            <button type="submit" class="btn btn-primary">
                                <?php _e('Update Profile', 'iptv-pro'); ?>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</main>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Tab switching
    const tabLinks = document.querySelectorAll('.tab-link');
    const tabContents = document.querySelectorAll('.tab-content');
    
    tabLinks.forEach(link => {
        link.addEventListener('click', function() {
            const targetTab = this.dataset.tab;
            
            // Update active tab
            tabLinks.forEach(l => {
                l.classList.remove('active');
                l.style.color = '#6b7280';
                l.style.borderBottom = 'none';
            });
            this.classList.add('active');
            this.style.color = '#3b82f6';
            this.style.borderBottom = '2px solid #3b82f6';
            
            // Show target content
            tabContents.forEach(content => {
                content.style.display = 'none';
                content.classList.remove('active');
            });
            const targetContent = document.getElementById(targetTab);
            if (targetContent) {
                targetContent.style.display = 'block';
                targetContent.classList.add('active');
            }
        });
    });
    
    // Password toggle
    const togglePassword = document.getElementById('toggle-password');
    const passwordHidden = document.getElementById('password-hidden');
    const passwordVisible = document.getElementById('password-visible');
    
    if (togglePassword) {
        togglePassword.addEventListener('click', function() {
            if (passwordVisible.style.display === 'none') {
                passwordHidden.style.display = 'none';
                passwordVisible.style.display = 'inline';
                this.textContent = '<?php _e('Hide', 'iptv-pro'); ?>';
            } else {
                passwordHidden.style.display = 'inline';
                passwordVisible.style.display = 'none';
                this.textContent = '<?php _e('Show', 'iptv-pro'); ?>';
            }
        });
    }
});

// Copy credentials function
function copyCredentials() {
    const username = '<?php echo esc_js($subscription_info['iptv_username']); ?>';
    const password = '<?php echo esc_js($subscription_info['iptv_password']); ?>';
    const server = '<?php echo esc_js($subscription_info['server_url']); ?>';
    
    const credentials = `Username: ${username}\nPassword: ${password}\nServer: ${server}`;
    
    navigator.clipboard.writeText(credentials).then(function() {
        alert('<?php _e('Credentials copied to clipboard!', 'iptv-pro'); ?>');
    });
}
</script>

<style>
@media (max-width: 768px) {
    .dashboard-tabs > div {
        flex-direction: column !important;
    }
    
    .tab-link {
        text-align: center !important;
        border-bottom: 1px solid #e5e7eb !important;
    }
    
    .tab-link.active {
        border-bottom: 2px solid #3b82f6 !important;
    }
}
</style>

<?php get_footer(); ?>
